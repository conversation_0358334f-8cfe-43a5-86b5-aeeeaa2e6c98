#!/usr/bin/env tsx

/**
 * Test script to verify that generated protobuf types are working correctly
 */

import { auth } from './src/types/generated/permissionservice';
import { edge } from './src/types/generated/edgedeviceproxy';

console.log('Testing generated protobuf types...\n');

// Test Permission Service types
console.log('=== Testing Permission Service Types ===');

// Test ValidateUserPermissionRequest
const permissionRequest = new auth.auth.ValidateUserPermissionRequest({
  user_id: 'test-user-123',
  feature_key: 'test-feature',
  operation: 1
});

console.log('ValidateUserPermissionRequest created:');
console.log('- user_id:', permissionRequest.user_id);
console.log('- feature_key:', permissionRequest.feature_key);
console.log('- operation:', permissionRequest.operation);
console.log('- toObject():', JSON.stringify(permissionRequest.toObject(), null, 2));

// Test ValidatePermissionResponse
const permissionResponse = new auth.auth.ValidatePermissionResponse({
  has_permission: true
});

console.log('\nValidatePermissionResponse created:');
console.log('- has_permission:', permissionResponse.has_permission);
console.log('- toObject():', JSON.stringify(permissionResponse.toObject(), null, 2));

// Test Edge Device Proxy types
console.log('\n=== Testing Edge Device Proxy Types ===');

// Test DeviceRequest with headers and metadata
const deviceRequest = new edge.v1.DeviceRequest({
  serial_number: 'DEVICE-123',
  path: '/api/test',
  priority: 1,
  async: false,
  headers: [
    new edge.v1.DeviceRequest.HeadersEntry({ key: 'Content-Type', value: 'application/json' }),
    new edge.v1.DeviceRequest.HeadersEntry({ key: 'Authorization', value: 'Bearer token123' })
  ],
  metadata: [
    new edge.v1.DeviceRequest.MetadataEntry({ key: 'source', value: 'test-script' }),
    new edge.v1.DeviceRequest.MetadataEntry({ key: 'version', value: '1.0' })
  ]
});

console.log('DeviceRequest created:');
console.log('- serial_number:', deviceRequest.serial_number);
console.log('- path:', deviceRequest.path);
console.log('- priority:', deviceRequest.priority);
console.log('- async:', deviceRequest.async);
console.log('- headers count:', deviceRequest.headers.length);
console.log('- metadata count:', deviceRequest.metadata.length);
console.log('- toObject():', JSON.stringify(deviceRequest.toObject(), null, 2));

// Test DeviceResponse
const deviceResponse = new edge.v1.DeviceResponse({
  status_code: 200,
  body: new Uint8Array([72, 101, 108, 108, 111]), // "Hello" in bytes
  headers: [
    new edge.v1.DeviceResponse.HeadersEntry({ key: 'Content-Type', value: 'text/plain' })
  ],
  request_id: 'req-123',
  is_async: false
});

console.log('\nDeviceResponse created:');
console.log('- status_code:', deviceResponse.status_code);
console.log('- body length:', deviceResponse.body.length);
console.log('- headers count:', deviceResponse.headers.length);
console.log('- request_id:', deviceResponse.request_id);
console.log('- is_async:', deviceResponse.is_async);
console.log('- toObject():', JSON.stringify(deviceResponse.toObject(), null, 2));

// Test serialization/deserialization
console.log('\n=== Testing Serialization/Deserialization ===');

const serialized = deviceRequest.serialize();
console.log('Serialized DeviceRequest length:', serialized.length, 'bytes');

const deserialized = edge.v1.DeviceRequest.deserialize(serialized);
console.log('Deserialized DeviceRequest:');
console.log('- serial_number:', deserialized.serial_number);
console.log('- path:', deserialized.path);
console.log('- headers count:', deserialized.headers.length);

console.log('\n✅ All generated types are working correctly!');
