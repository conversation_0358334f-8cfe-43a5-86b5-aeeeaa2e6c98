# -----------------------------------------------
# !!! THIS FILE WAS GENERATED BY TYPE-GRAPHQL !!!
# !!!   DO NOT MODIFY THIS FILE BY YOURSELF   !!!
# -----------------------------------------------

"""
A date-time string at UTC, such as 2007-12-03T10:15:30Z, compliant with the `date-time` format outlined in section 5.6 of the RFC 3339 profile of the ISO 8601 standard for representation of dates and times using the Gregorian calendar.This scalar is serialized to a string in ISO 8601 format and parsed from a string in ISO 8601 format.
"""
scalar DateTimeISO

type EpikBoxDocument {
  _id: ID!
  creationDate: DateTimeISO
  deleted: Boolean
  lastSeen: DateTimeISO
  lastUpdated: DateTimeISO
  serialNumber: String!
  status: String
  vpnAddress: String!
}

input EpikBoxFilterInput {
  serialNumber: String
  status: String
  vpnAddress: String
}

type EpikBoxPaginationResult {
  docs: [EpikBoxDocument!]!
  pagination: PaginationInfo!
}

type PaginationInfo {
  count: Int!
  currentPage: Int!
  totalPages: Int!
}

input PaginationInput {
  page: Int! = 1
  pageSize: Int! = 20
}

type Query {
  epikBoxes(filter: EpikBoxFilterInput, pagination: PaginationInput): EpikBoxPaginationResult!
}