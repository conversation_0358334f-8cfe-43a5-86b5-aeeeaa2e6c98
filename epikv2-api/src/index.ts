import { createApp } from '@/app';
import { config } from '@/config/environment';
import { logger } from '@/utils/logger';

async function startServer(): Promise<void> {
  try {
    const app = await createApp();

    const address = await app.listen({
      port: config.port,
      host: config.host,
    });

    logger.info(`🚀 Server running at ${address}`);
    logger.info(`📚 API Documentation: ${address}/docs`);
    logger.info(`🔍 Health Check: ${address}/health`);

    const gracefulShutdown = async (signal: string): Promise<void> => {
      logger.info(`Received ${signal}, shutting down gracefully...`);

      try {
        await app.close();
        logger.info('Server closed successfully');
        process.exit(0);
      } catch (error) {
        logger.error('Error during shutdown:', error);
        process.exit(1);
      }
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
  } catch (error) {
    console.log(error);
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Handle uncaught exceptions
process.on('uncaughtException', error => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Start the server
startServer();
