import { Services } from '@/services';
import { JWT } from '@fastify/jwt';
import { FastifyMongoNestedObject, FastifyMongoObject } from '@fastify/mongodb';
import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';

import { GraphQLScalarType, Kind } from 'graphql';
import { ObjectId } from 'mongodb';
import { Features, ListAccessResponse, ValidateOperations } from './permissions';

export interface JWTPayload {
  id: string;
  email: string;
  roles: string[];
  iat: number;
  exp: number;
}

// Permission handler type
export type PermissionHandler<T = any> = (
  request: FastifyRequest,
  reply: FastifyReply
) => Promise<T>;

declare module 'fastify' {
  interface FastifyRequest {
    user: JWTPayload;
    jwtVerify(): Promise<JWTPayload>;
  }

  interface FastifyInstance {
    authenticate: (request: FastifyRequest, reply: FastifyReply) => Promise<void>;
    jwt: JWT;
    mongo: FastifyMongoObject & FastifyMongoNestedObject;
    // Permission decorators
    requirePermission: (
      request: FastifyRequest,
      featureKey: Features,
      operation: ValidateOperations
    ) => Promise<Boolean>;
    requirePermissionHook: (
      featureKey: Features,
      operation: ValidateOperations
    ) => PermissionHandler;
    // Company and enterprise access
    requireCompanyAccess: (request: FastifyRequest, companyIdParam?: string) => Promise<Boolean>;
    requireEnterpriseAccess: (
      request: FastifyRequest,
      enterpriseIdParam?: string
    ) => Promise<Boolean>;
    listAllowedCompanies: (request: FastifyRequest) => Promise<ListAccessResponse>;
    listAllowedEnterprises: (request: FastifyRequest) => Promise<ListAccessResponse>;

    services: Services;
  }
}

declare module '@fastify/jwt' {
  interface FastifyJWT {
    payload: JWTPayload;
    user: JWTPayload;
  }
}

export interface GraphqlContext {
  reply: FastifyReply;
  request: FastifyRequest;
  fastify: FastifyInstance;
  user: JWTPayload | null;
}
export const ObjectIdScalar = new GraphQLScalarType({
  name: 'OID',
  description: 'Mongo object id scalar type',
  serialize(value: unknown): string {
    if (!(value instanceof ObjectId)) {
      throw new Error('ObjectIdScalar can only serialize ObjectId values');
    }
    return value.toHexString();
  },
  parseValue(value: unknown): ObjectId {
    if (typeof value !== 'string') {
      throw new Error('ObjectIdScalar can only parse string values');
    }
    return new ObjectId(value);
  },
  parseLiteral(ast): ObjectId {
    if (ast.kind !== Kind.STRING) {
      throw new Error('ObjectIdScalar can only parse string values');
    }
    return new ObjectId(ast.value);
  },
});
