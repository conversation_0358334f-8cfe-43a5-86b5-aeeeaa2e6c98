import { IsOptional } from 'class-validator';
import { ObjectType, Field } from 'type-graphql';
import { ObjectId } from 'mongodb';
import { NumberDocument } from '@/models';

@ObjectType()
export class PortOutput {
  @Field(() => String, { nullable: true })
  @IsOptional()
  boxPortNumber: string;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  registered: boolean;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  disabled: boolean;

  @Field(() => String, { nullable: true })
  @IsOptional()
  assignedNumber: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  serviceName?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  provisionDate?: string;

  @Field(() => Date, { nullable: true })
  @IsOptional()
  updatedOn?: Date;  

  @Field(() => ObjectId, { nullable: true })
  @IsOptional()
  assignedNumberRef?: ObjectId;

  @Field(() => NumberDocument, { nullable: true })
  assignedNumberDoc?: NumberDocument;
}