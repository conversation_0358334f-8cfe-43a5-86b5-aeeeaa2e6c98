import { EpikBoxDocument } from '@/models';
import { IsOptional, <PERSON>, Min } from 'class-validator';
import { Document, ObjectId, Sort } from 'mongodb';
import { ClassType, Field, InputType, Int, ObjectType } from 'type-graphql';

// Base document interface that all document types should extend
export interface BaseDocument extends Document {
  _id?: ObjectId;
  creationDate?: Date;
  lastUpdated?: Date;
  deleted?: boolean;
}

export interface MongoQueryOptions {
  sort?: Sort;
  projection?: Document;
  batchSize?: number;
}


@ObjectType()
export class PaginationResponseOptions {
  @Field(() => Int)
  currentPage: number;

  @Field(() => Int)
  totalPages: number;

  @Field(() => Int)
  count: number;
}

@InputType()
export class PaginationInput {
  @Field(() => Int, { defaultValue: 1 })
  @IsOptional()
  @Min(1)
  page: number = 1;

  @Field(() => Int, { defaultValue: 10 })
  @IsOptional()
  @Min(1)
  @Max(100)
  pageSize: number = 10;
}

export function createPaginationResult<T>(ItemClass: ClassType<T extends Object ? T : never>) {
  @ObjectType(`${ItemClass.name}PaginationResult`)
  class PaginationResult {
    @Field(() => [ItemClass], { description: 'Array of documents' })
    docs: T[];

    @Field(() => PaginationResponseOptions)
    pagination: PaginationResponseOptions;
  }

  return PaginationResult;
}
export type PaginatedResult<T> = InstanceType<ReturnType<typeof createPaginationResult<T>>>;

export const ListEpikBoxPaginatedResult = createPaginationResult<EpikBoxDocument>(EpikBoxDocument);

export type ListEpikBoxPaginatedResultType = PaginatedResult<EpikBoxDocument>;
