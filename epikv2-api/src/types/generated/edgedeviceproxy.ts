/**
 * Generated by the protoc-gen-ts.  DO NOT EDIT!
 * compiler version: 3.21.12
 * source: edgedeviceproxy.proto
 * git: https://github.com/thesayyn/protoc-gen-ts */
import * as pb_1 from "google-protobuf";
import * as grpc_1 from "@grpc/grpc-js";
export namespace edge.v1 {
    export class DeviceRequest extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            serial_number?: string;
            path?: string;
            priority?: number;
            async?: boolean;
            headers?: DeviceRequest.HeadersEntry[];
            metadata?: DeviceRequest.MetadataEntry[];
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [5, 6], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("serial_number" in data && data.serial_number != undefined) {
                    this.serial_number = data.serial_number;
                }
                if ("path" in data && data.path != undefined) {
                    this.path = data.path;
                }
                if ("priority" in data && data.priority != undefined) {
                    this.priority = data.priority;
                }
                if ("async" in data && data.async != undefined) {
                    this.async = data.async;
                }
                if ("headers" in data && data.headers != undefined) {
                    this.headers = data.headers;
                }
                if ("metadata" in data && data.metadata != undefined) {
                    this.metadata = data.metadata;
                }
            }
        }
        get serial_number() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set serial_number(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get path() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set path(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        get priority() {
            return pb_1.Message.getFieldWithDefault(this, 3, 0) as number;
        }
        set priority(value: number) {
            pb_1.Message.setField(this, 3, value);
        }
        get async() {
            return pb_1.Message.getFieldWithDefault(this, 4, false) as boolean;
        }
        set async(value: boolean) {
            pb_1.Message.setField(this, 4, value);
        }
        get headers() {
            return pb_1.Message.getRepeatedWrapperField(this, DeviceRequest.HeadersEntry, 5) as DeviceRequest.HeadersEntry[];
        }
        set headers(value: DeviceRequest.HeadersEntry[]) {
            pb_1.Message.setRepeatedWrapperField(this, 5, value);
        }
        get metadata() {
            return pb_1.Message.getRepeatedWrapperField(this, DeviceRequest.MetadataEntry, 6) as DeviceRequest.MetadataEntry[];
        }
        set metadata(value: DeviceRequest.MetadataEntry[]) {
            pb_1.Message.setRepeatedWrapperField(this, 6, value);
        }
        static fromObject(data: {
            serial_number?: string;
            path?: string;
            priority?: number;
            async?: boolean;
            headers?: ReturnType<typeof DeviceRequest.HeadersEntry.prototype.toObject>[];
            metadata?: ReturnType<typeof DeviceRequest.MetadataEntry.prototype.toObject>[];
        }): DeviceRequest {
            const message = new DeviceRequest({});
            if (data.serial_number != null) {
                message.serial_number = data.serial_number;
            }
            if (data.path != null) {
                message.path = data.path;
            }
            if (data.priority != null) {
                message.priority = data.priority;
            }
            if (data.async != null) {
                message.async = data.async;
            }
            if (data.headers != null) {
                message.headers = data.headers.map(item => DeviceRequest.HeadersEntry.fromObject(item));
            }
            if (data.metadata != null) {
                message.metadata = data.metadata.map(item => DeviceRequest.MetadataEntry.fromObject(item));
            }
            return message;
        }
        toObject() {
            const data: {
                serial_number?: string;
                path?: string;
                priority?: number;
                async?: boolean;
                headers?: ReturnType<typeof DeviceRequest.HeadersEntry.prototype.toObject>[];
                metadata?: ReturnType<typeof DeviceRequest.MetadataEntry.prototype.toObject>[];
            } = {};
            if (this.serial_number != null) {
                data.serial_number = this.serial_number;
            }
            if (this.path != null) {
                data.path = this.path;
            }
            if (this.priority != null) {
                data.priority = this.priority;
            }
            if (this.async != null) {
                data.async = this.async;
            }
            if (this.headers != null) {
                data.headers = this.headers.map((item: DeviceRequest.HeadersEntry) => item.toObject());
            }
            if (this.metadata != null) {
                data.metadata = this.metadata.map((item: DeviceRequest.MetadataEntry) => item.toObject());
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.serial_number.length)
                writer.writeString(1, this.serial_number);
            if (this.path.length)
                writer.writeString(2, this.path);
            if (this.priority != 0)
                writer.writeInt32(3, this.priority);
            if (this.async != false)
                writer.writeBool(4, this.async);
            if (this.headers.length)
                writer.writeRepeatedMessage(5, this.headers, (item: DeviceRequest.HeadersEntry) => item.serialize(writer));
            if (this.metadata.length)
                writer.writeRepeatedMessage(6, this.metadata, (item: DeviceRequest.MetadataEntry) => item.serialize(writer));
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): DeviceRequest {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new DeviceRequest();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.serial_number = reader.readString();
                        break;
                    case 2:
                        message.path = reader.readString();
                        break;
                    case 3:
                        message.priority = reader.readInt32();
                        break;
                    case 4:
                        message.async = reader.readBool();
                        break;
                    case 5:
                        reader.readMessage(message.headers, () => pb_1.Message.addToRepeatedWrapperField(message, 5, DeviceRequest.HeadersEntry.deserialize(reader), DeviceRequest.HeadersEntry));
                        break;
                    case 6:
                        reader.readMessage(message.metadata, () => pb_1.Message.addToRepeatedWrapperField(message, 6, DeviceRequest.MetadataEntry.deserialize(reader), DeviceRequest.MetadataEntry));
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): DeviceRequest {
            return DeviceRequest.deserialize(bytes);
        }
    }
    export namespace DeviceRequest {
        export class HeadersEntry extends pb_1.Message {
            #one_of_decls: number[][] = [];
            constructor(data?: any[] | {
                key?: string;
                value?: string;
            }) {
                super();
                pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
                if (!Array.isArray(data) && typeof data == "object") {
                    if ("key" in data && data.key != undefined) {
                        this.key = data.key;
                    }
                    if ("value" in data && data.value != undefined) {
                        this.value = data.value;
                    }
                }
            }
            get key() {
                return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
            }
            set key(value: string) {
                pb_1.Message.setField(this, 1, value);
            }
            get value() {
                return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
            }
            set value(value: string) {
                pb_1.Message.setField(this, 2, value);
            }
            static fromObject(data: {
                key?: string;
                value?: string;
            }): HeadersEntry {
                const message = new HeadersEntry({});
                if (data.key != null) {
                    message.key = data.key;
                }
                if (data.value != null) {
                    message.value = data.value;
                }
                return message;
            }
            toObject() {
                const data: {
                    key?: string;
                    value?: string;
                } = {};
                if (this.key != null) {
                    data.key = this.key;
                }
                if (this.value != null) {
                    data.value = this.value;
                }
                return data;
            }
            serialize(): Uint8Array;
            serialize(w: pb_1.BinaryWriter): void;
            serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
                const writer = w || new pb_1.BinaryWriter();
                if (this.key.length)
                    writer.writeString(1, this.key);
                if (this.value.length)
                    writer.writeString(2, this.value);
                if (!w)
                    return writer.getResultBuffer();
            }
            static deserialize(bytes: Uint8Array | pb_1.BinaryReader): HeadersEntry {
                const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new HeadersEntry();
                while (reader.nextField()) {
                    if (reader.isEndGroup())
                        break;
                    switch (reader.getFieldNumber()) {
                        case 1:
                            message.key = reader.readString();
                            break;
                        case 2:
                            message.value = reader.readString();
                            break;
                        default: reader.skipField();
                    }
                }
                return message;
            }
            serializeBinary(): Uint8Array {
                return this.serialize();
            }
            static deserializeBinary(bytes: Uint8Array): HeadersEntry {
                return HeadersEntry.deserialize(bytes);
            }
        }
        export class MetadataEntry extends pb_1.Message {
            #one_of_decls: number[][] = [];
            constructor(data?: any[] | {
                key?: string;
                value?: string;
            }) {
                super();
                pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
                if (!Array.isArray(data) && typeof data == "object") {
                    if ("key" in data && data.key != undefined) {
                        this.key = data.key;
                    }
                    if ("value" in data && data.value != undefined) {
                        this.value = data.value;
                    }
                }
            }
            get key() {
                return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
            }
            set key(value: string) {
                pb_1.Message.setField(this, 1, value);
            }
            get value() {
                return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
            }
            set value(value: string) {
                pb_1.Message.setField(this, 2, value);
            }
            static fromObject(data: {
                key?: string;
                value?: string;
            }): MetadataEntry {
                const message = new MetadataEntry({});
                if (data.key != null) {
                    message.key = data.key;
                }
                if (data.value != null) {
                    message.value = data.value;
                }
                return message;
            }
            toObject() {
                const data: {
                    key?: string;
                    value?: string;
                } = {};
                if (this.key != null) {
                    data.key = this.key;
                }
                if (this.value != null) {
                    data.value = this.value;
                }
                return data;
            }
            serialize(): Uint8Array;
            serialize(w: pb_1.BinaryWriter): void;
            serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
                const writer = w || new pb_1.BinaryWriter();
                if (this.key.length)
                    writer.writeString(1, this.key);
                if (this.value.length)
                    writer.writeString(2, this.value);
                if (!w)
                    return writer.getResultBuffer();
            }
            static deserialize(bytes: Uint8Array | pb_1.BinaryReader): MetadataEntry {
                const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new MetadataEntry();
                while (reader.nextField()) {
                    if (reader.isEndGroup())
                        break;
                    switch (reader.getFieldNumber()) {
                        case 1:
                            message.key = reader.readString();
                            break;
                        case 2:
                            message.value = reader.readString();
                            break;
                        default: reader.skipField();
                    }
                }
                return message;
            }
            serializeBinary(): Uint8Array {
                return this.serialize();
            }
            static deserializeBinary(bytes: Uint8Array): MetadataEntry {
                return MetadataEntry.deserialize(bytes);
            }
        }
    }
    export class DeviceResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            status_code?: number;
            body?: Uint8Array;
            headers?: DeviceResponse.HeadersEntry[];
            request_id?: string;
            is_async?: boolean;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [3], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("status_code" in data && data.status_code != undefined) {
                    this.status_code = data.status_code;
                }
                if ("body" in data && data.body != undefined) {
                    this.body = data.body;
                }
                if ("headers" in data && data.headers != undefined) {
                    this.headers = data.headers;
                }
                if ("request_id" in data && data.request_id != undefined) {
                    this.request_id = data.request_id;
                }
                if ("is_async" in data && data.is_async != undefined) {
                    this.is_async = data.is_async;
                }
            }
        }
        get status_code() {
            return pb_1.Message.getFieldWithDefault(this, 1, 0) as number;
        }
        set status_code(value: number) {
            pb_1.Message.setField(this, 1, value);
        }
        get body() {
            return pb_1.Message.getFieldWithDefault(this, 2, new Uint8Array(0)) as Uint8Array;
        }
        set body(value: Uint8Array) {
            pb_1.Message.setField(this, 2, value);
        }
        get headers() {
            return pb_1.Message.getRepeatedWrapperField(this, DeviceResponse.HeadersEntry, 3) as DeviceResponse.HeadersEntry[];
        }
        set headers(value: DeviceResponse.HeadersEntry[]) {
            pb_1.Message.setRepeatedWrapperField(this, 3, value);
        }
        get request_id() {
            return pb_1.Message.getFieldWithDefault(this, 4, "") as string;
        }
        set request_id(value: string) {
            pb_1.Message.setField(this, 4, value);
        }
        get is_async() {
            return pb_1.Message.getFieldWithDefault(this, 5, false) as boolean;
        }
        set is_async(value: boolean) {
            pb_1.Message.setField(this, 5, value);
        }
        static fromObject(data: {
            status_code?: number;
            body?: Uint8Array;
            headers?: ReturnType<typeof DeviceResponse.HeadersEntry.prototype.toObject>[];
            request_id?: string;
            is_async?: boolean;
        }): DeviceResponse {
            const message = new DeviceResponse({});
            if (data.status_code != null) {
                message.status_code = data.status_code;
            }
            if (data.body != null) {
                message.body = data.body;
            }
            if (data.headers != null) {
                message.headers = data.headers.map(item => DeviceResponse.HeadersEntry.fromObject(item));
            }
            if (data.request_id != null) {
                message.request_id = data.request_id;
            }
            if (data.is_async != null) {
                message.is_async = data.is_async;
            }
            return message;
        }
        toObject() {
            const data: {
                status_code?: number;
                body?: Uint8Array;
                headers?: ReturnType<typeof DeviceResponse.HeadersEntry.prototype.toObject>[];
                request_id?: string;
                is_async?: boolean;
            } = {};
            if (this.status_code != null) {
                data.status_code = this.status_code;
            }
            if (this.body != null) {
                data.body = this.body;
            }
            if (this.headers != null) {
                data.headers = this.headers.map((item: DeviceResponse.HeadersEntry) => item.toObject());
            }
            if (this.request_id != null) {
                data.request_id = this.request_id;
            }
            if (this.is_async != null) {
                data.is_async = this.is_async;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.status_code != 0)
                writer.writeInt32(1, this.status_code);
            if (this.body.length)
                writer.writeBytes(2, this.body);
            if (this.headers.length)
                writer.writeRepeatedMessage(3, this.headers, (item: DeviceResponse.HeadersEntry) => item.serialize(writer));
            if (this.request_id.length)
                writer.writeString(4, this.request_id);
            if (this.is_async != false)
                writer.writeBool(5, this.is_async);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): DeviceResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new DeviceResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.status_code = reader.readInt32();
                        break;
                    case 2:
                        message.body = reader.readBytes();
                        break;
                    case 3:
                        reader.readMessage(message.headers, () => pb_1.Message.addToRepeatedWrapperField(message, 3, DeviceResponse.HeadersEntry.deserialize(reader), DeviceResponse.HeadersEntry));
                        break;
                    case 4:
                        message.request_id = reader.readString();
                        break;
                    case 5:
                        message.is_async = reader.readBool();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): DeviceResponse {
            return DeviceResponse.deserialize(bytes);
        }
    }
    export namespace DeviceResponse {
        export class HeadersEntry extends pb_1.Message {
            #one_of_decls: number[][] = [];
            constructor(data?: any[] | {
                key?: string;
                value?: string;
            }) {
                super();
                pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
                if (!Array.isArray(data) && typeof data == "object") {
                    if ("key" in data && data.key != undefined) {
                        this.key = data.key;
                    }
                    if ("value" in data && data.value != undefined) {
                        this.value = data.value;
                    }
                }
            }
            get key() {
                return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
            }
            set key(value: string) {
                pb_1.Message.setField(this, 1, value);
            }
            get value() {
                return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
            }
            set value(value: string) {
                pb_1.Message.setField(this, 2, value);
            }
            static fromObject(data: {
                key?: string;
                value?: string;
            }): HeadersEntry {
                const message = new HeadersEntry({});
                if (data.key != null) {
                    message.key = data.key;
                }
                if (data.value != null) {
                    message.value = data.value;
                }
                return message;
            }
            toObject() {
                const data: {
                    key?: string;
                    value?: string;
                } = {};
                if (this.key != null) {
                    data.key = this.key;
                }
                if (this.value != null) {
                    data.value = this.value;
                }
                return data;
            }
            serialize(): Uint8Array;
            serialize(w: pb_1.BinaryWriter): void;
            serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
                const writer = w || new pb_1.BinaryWriter();
                if (this.key.length)
                    writer.writeString(1, this.key);
                if (this.value.length)
                    writer.writeString(2, this.value);
                if (!w)
                    return writer.getResultBuffer();
            }
            static deserialize(bytes: Uint8Array | pb_1.BinaryReader): HeadersEntry {
                const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new HeadersEntry();
                while (reader.nextField()) {
                    if (reader.isEndGroup())
                        break;
                    switch (reader.getFieldNumber()) {
                        case 1:
                            message.key = reader.readString();
                            break;
                        case 2:
                            message.value = reader.readString();
                            break;
                        default: reader.skipField();
                    }
                }
                return message;
            }
            serializeBinary(): Uint8Array {
                return this.serialize();
            }
            static deserializeBinary(bytes: Uint8Array): HeadersEntry {
                return HeadersEntry.deserialize(bytes);
            }
        }
    }
    export class AsyncRequest extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            serial_number?: string;
            path?: string;
            priority?: number;
            headers?: AsyncRequest.HeadersEntry[];
            metadata?: AsyncRequest.MetadataEntry[];
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [4, 5], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("serial_number" in data && data.serial_number != undefined) {
                    this.serial_number = data.serial_number;
                }
                if ("path" in data && data.path != undefined) {
                    this.path = data.path;
                }
                if ("priority" in data && data.priority != undefined) {
                    this.priority = data.priority;
                }
                if ("headers" in data && data.headers != undefined) {
                    this.headers = data.headers;
                }
                if ("metadata" in data && data.metadata != undefined) {
                    this.metadata = data.metadata;
                }
            }
        }
        get serial_number() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set serial_number(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get path() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set path(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        get priority() {
            return pb_1.Message.getFieldWithDefault(this, 3, 0) as number;
        }
        set priority(value: number) {
            pb_1.Message.setField(this, 3, value);
        }
        get headers() {
            return pb_1.Message.getRepeatedWrapperField(this, AsyncRequest.HeadersEntry, 4) as AsyncRequest.HeadersEntry[];
        }
        set headers(value: AsyncRequest.HeadersEntry[]) {
            pb_1.Message.setRepeatedWrapperField(this, 4, value);
        }
        get metadata() {
            return pb_1.Message.getRepeatedWrapperField(this, AsyncRequest.MetadataEntry, 5) as AsyncRequest.MetadataEntry[];
        }
        set metadata(value: AsyncRequest.MetadataEntry[]) {
            pb_1.Message.setRepeatedWrapperField(this, 5, value);
        }
        static fromObject(data: {
            serial_number?: string;
            path?: string;
            priority?: number;
            headers?: ReturnType<typeof AsyncRequest.HeadersEntry.prototype.toObject>[];
            metadata?: ReturnType<typeof AsyncRequest.MetadataEntry.prototype.toObject>[];
        }): AsyncRequest {
            const message = new AsyncRequest({});
            if (data.serial_number != null) {
                message.serial_number = data.serial_number;
            }
            if (data.path != null) {
                message.path = data.path;
            }
            if (data.priority != null) {
                message.priority = data.priority;
            }
            if (data.headers != null) {
                message.headers = data.headers.map(item => AsyncRequest.HeadersEntry.fromObject(item));
            }
            if (data.metadata != null) {
                message.metadata = data.metadata.map(item => AsyncRequest.MetadataEntry.fromObject(item));
            }
            return message;
        }
        toObject() {
            const data: {
                serial_number?: string;
                path?: string;
                priority?: number;
                headers?: ReturnType<typeof AsyncRequest.HeadersEntry.prototype.toObject>[];
                metadata?: ReturnType<typeof AsyncRequest.MetadataEntry.prototype.toObject>[];
            } = {};
            if (this.serial_number != null) {
                data.serial_number = this.serial_number;
            }
            if (this.path != null) {
                data.path = this.path;
            }
            if (this.priority != null) {
                data.priority = this.priority;
            }
            if (this.headers != null) {
                data.headers = this.headers.map((item: AsyncRequest.HeadersEntry) => item.toObject());
            }
            if (this.metadata != null) {
                data.metadata = this.metadata.map((item: AsyncRequest.MetadataEntry) => item.toObject());
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.serial_number.length)
                writer.writeString(1, this.serial_number);
            if (this.path.length)
                writer.writeString(2, this.path);
            if (this.priority != 0)
                writer.writeInt32(3, this.priority);
            if (this.headers.length)
                writer.writeRepeatedMessage(4, this.headers, (item: AsyncRequest.HeadersEntry) => item.serialize(writer));
            if (this.metadata.length)
                writer.writeRepeatedMessage(5, this.metadata, (item: AsyncRequest.MetadataEntry) => item.serialize(writer));
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): AsyncRequest {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new AsyncRequest();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.serial_number = reader.readString();
                        break;
                    case 2:
                        message.path = reader.readString();
                        break;
                    case 3:
                        message.priority = reader.readInt32();
                        break;
                    case 4:
                        reader.readMessage(message.headers, () => pb_1.Message.addToRepeatedWrapperField(message, 4, AsyncRequest.HeadersEntry.deserialize(reader), AsyncRequest.HeadersEntry));
                        break;
                    case 5:
                        reader.readMessage(message.metadata, () => pb_1.Message.addToRepeatedWrapperField(message, 5, AsyncRequest.MetadataEntry.deserialize(reader), AsyncRequest.MetadataEntry));
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): AsyncRequest {
            return AsyncRequest.deserialize(bytes);
        }
    }
    export namespace AsyncRequest {
        export class HeadersEntry extends pb_1.Message {
            #one_of_decls: number[][] = [];
            constructor(data?: any[] | {
                key?: string;
                value?: string;
            }) {
                super();
                pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
                if (!Array.isArray(data) && typeof data == "object") {
                    if ("key" in data && data.key != undefined) {
                        this.key = data.key;
                    }
                    if ("value" in data && data.value != undefined) {
                        this.value = data.value;
                    }
                }
            }
            get key() {
                return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
            }
            set key(value: string) {
                pb_1.Message.setField(this, 1, value);
            }
            get value() {
                return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
            }
            set value(value: string) {
                pb_1.Message.setField(this, 2, value);
            }
            static fromObject(data: {
                key?: string;
                value?: string;
            }): HeadersEntry {
                const message = new HeadersEntry({});
                if (data.key != null) {
                    message.key = data.key;
                }
                if (data.value != null) {
                    message.value = data.value;
                }
                return message;
            }
            toObject() {
                const data: {
                    key?: string;
                    value?: string;
                } = {};
                if (this.key != null) {
                    data.key = this.key;
                }
                if (this.value != null) {
                    data.value = this.value;
                }
                return data;
            }
            serialize(): Uint8Array;
            serialize(w: pb_1.BinaryWriter): void;
            serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
                const writer = w || new pb_1.BinaryWriter();
                if (this.key.length)
                    writer.writeString(1, this.key);
                if (this.value.length)
                    writer.writeString(2, this.value);
                if (!w)
                    return writer.getResultBuffer();
            }
            static deserialize(bytes: Uint8Array | pb_1.BinaryReader): HeadersEntry {
                const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new HeadersEntry();
                while (reader.nextField()) {
                    if (reader.isEndGroup())
                        break;
                    switch (reader.getFieldNumber()) {
                        case 1:
                            message.key = reader.readString();
                            break;
                        case 2:
                            message.value = reader.readString();
                            break;
                        default: reader.skipField();
                    }
                }
                return message;
            }
            serializeBinary(): Uint8Array {
                return this.serialize();
            }
            static deserializeBinary(bytes: Uint8Array): HeadersEntry {
                return HeadersEntry.deserialize(bytes);
            }
        }
        export class MetadataEntry extends pb_1.Message {
            #one_of_decls: number[][] = [];
            constructor(data?: any[] | {
                key?: string;
                value?: string;
            }) {
                super();
                pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
                if (!Array.isArray(data) && typeof data == "object") {
                    if ("key" in data && data.key != undefined) {
                        this.key = data.key;
                    }
                    if ("value" in data && data.value != undefined) {
                        this.value = data.value;
                    }
                }
            }
            get key() {
                return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
            }
            set key(value: string) {
                pb_1.Message.setField(this, 1, value);
            }
            get value() {
                return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
            }
            set value(value: string) {
                pb_1.Message.setField(this, 2, value);
            }
            static fromObject(data: {
                key?: string;
                value?: string;
            }): MetadataEntry {
                const message = new MetadataEntry({});
                if (data.key != null) {
                    message.key = data.key;
                }
                if (data.value != null) {
                    message.value = data.value;
                }
                return message;
            }
            toObject() {
                const data: {
                    key?: string;
                    value?: string;
                } = {};
                if (this.key != null) {
                    data.key = this.key;
                }
                if (this.value != null) {
                    data.value = this.value;
                }
                return data;
            }
            serialize(): Uint8Array;
            serialize(w: pb_1.BinaryWriter): void;
            serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
                const writer = w || new pb_1.BinaryWriter();
                if (this.key.length)
                    writer.writeString(1, this.key);
                if (this.value.length)
                    writer.writeString(2, this.value);
                if (!w)
                    return writer.getResultBuffer();
            }
            static deserialize(bytes: Uint8Array | pb_1.BinaryReader): MetadataEntry {
                const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new MetadataEntry();
                while (reader.nextField()) {
                    if (reader.isEndGroup())
                        break;
                    switch (reader.getFieldNumber()) {
                        case 1:
                            message.key = reader.readString();
                            break;
                        case 2:
                            message.value = reader.readString();
                            break;
                        default: reader.skipField();
                    }
                }
                return message;
            }
            serializeBinary(): Uint8Array {
                return this.serialize();
            }
            static deserializeBinary(bytes: Uint8Array): MetadataEntry {
                return MetadataEntry.deserialize(bytes);
            }
        }
    }
    export class AsyncResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            request_id?: string;
            status?: string;
            estimated_completion_seconds?: number;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("request_id" in data && data.request_id != undefined) {
                    this.request_id = data.request_id;
                }
                if ("status" in data && data.status != undefined) {
                    this.status = data.status;
                }
                if ("estimated_completion_seconds" in data && data.estimated_completion_seconds != undefined) {
                    this.estimated_completion_seconds = data.estimated_completion_seconds;
                }
            }
        }
        get request_id() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set request_id(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get status() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set status(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        get estimated_completion_seconds() {
            return pb_1.Message.getFieldWithDefault(this, 3, 0) as number;
        }
        set estimated_completion_seconds(value: number) {
            pb_1.Message.setField(this, 3, value);
        }
        static fromObject(data: {
            request_id?: string;
            status?: string;
            estimated_completion_seconds?: number;
        }): AsyncResponse {
            const message = new AsyncResponse({});
            if (data.request_id != null) {
                message.request_id = data.request_id;
            }
            if (data.status != null) {
                message.status = data.status;
            }
            if (data.estimated_completion_seconds != null) {
                message.estimated_completion_seconds = data.estimated_completion_seconds;
            }
            return message;
        }
        toObject() {
            const data: {
                request_id?: string;
                status?: string;
                estimated_completion_seconds?: number;
            } = {};
            if (this.request_id != null) {
                data.request_id = this.request_id;
            }
            if (this.status != null) {
                data.status = this.status;
            }
            if (this.estimated_completion_seconds != null) {
                data.estimated_completion_seconds = this.estimated_completion_seconds;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.request_id.length)
                writer.writeString(1, this.request_id);
            if (this.status.length)
                writer.writeString(2, this.status);
            if (this.estimated_completion_seconds != 0)
                writer.writeInt64(3, this.estimated_completion_seconds);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): AsyncResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new AsyncResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.request_id = reader.readString();
                        break;
                    case 2:
                        message.status = reader.readString();
                        break;
                    case 3:
                        message.estimated_completion_seconds = reader.readInt64();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): AsyncResponse {
            return AsyncResponse.deserialize(bytes);
        }
    }
    export class StatusRequest extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            request_id?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("request_id" in data && data.request_id != undefined) {
                    this.request_id = data.request_id;
                }
            }
        }
        get request_id() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set request_id(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        static fromObject(data: {
            request_id?: string;
        }): StatusRequest {
            const message = new StatusRequest({});
            if (data.request_id != null) {
                message.request_id = data.request_id;
            }
            return message;
        }
        toObject() {
            const data: {
                request_id?: string;
            } = {};
            if (this.request_id != null) {
                data.request_id = this.request_id;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.request_id.length)
                writer.writeString(1, this.request_id);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): StatusRequest {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new StatusRequest();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.request_id = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): StatusRequest {
            return StatusRequest.deserialize(bytes);
        }
    }
    export class StatusResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            request_id?: string;
            status?: string;
            status_code?: number;
            body?: Uint8Array;
            headers?: StatusResponse.HeadersEntry[];
            error?: string;
            created_at?: number;
            completed_at?: number;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [5], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("request_id" in data && data.request_id != undefined) {
                    this.request_id = data.request_id;
                }
                if ("status" in data && data.status != undefined) {
                    this.status = data.status;
                }
                if ("status_code" in data && data.status_code != undefined) {
                    this.status_code = data.status_code;
                }
                if ("body" in data && data.body != undefined) {
                    this.body = data.body;
                }
                if ("headers" in data && data.headers != undefined) {
                    this.headers = data.headers;
                }
                if ("error" in data && data.error != undefined) {
                    this.error = data.error;
                }
                if ("created_at" in data && data.created_at != undefined) {
                    this.created_at = data.created_at;
                }
                if ("completed_at" in data && data.completed_at != undefined) {
                    this.completed_at = data.completed_at;
                }
            }
        }
        get request_id() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set request_id(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get status() {
            return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
        }
        set status(value: string) {
            pb_1.Message.setField(this, 2, value);
        }
        get status_code() {
            return pb_1.Message.getFieldWithDefault(this, 3, 0) as number;
        }
        set status_code(value: number) {
            pb_1.Message.setField(this, 3, value);
        }
        get body() {
            return pb_1.Message.getFieldWithDefault(this, 4, new Uint8Array(0)) as Uint8Array;
        }
        set body(value: Uint8Array) {
            pb_1.Message.setField(this, 4, value);
        }
        get headers() {
            return pb_1.Message.getRepeatedWrapperField(this, StatusResponse.HeadersEntry, 5) as StatusResponse.HeadersEntry[];
        }
        set headers(value: StatusResponse.HeadersEntry[]) {
            pb_1.Message.setRepeatedWrapperField(this, 5, value);
        }
        get error() {
            return pb_1.Message.getFieldWithDefault(this, 6, "") as string;
        }
        set error(value: string) {
            pb_1.Message.setField(this, 6, value);
        }
        get created_at() {
            return pb_1.Message.getFieldWithDefault(this, 7, 0) as number;
        }
        set created_at(value: number) {
            pb_1.Message.setField(this, 7, value);
        }
        get completed_at() {
            return pb_1.Message.getFieldWithDefault(this, 8, 0) as number;
        }
        set completed_at(value: number) {
            pb_1.Message.setField(this, 8, value);
        }
        static fromObject(data: {
            request_id?: string;
            status?: string;
            status_code?: number;
            body?: Uint8Array;
            headers?: ReturnType<typeof StatusResponse.HeadersEntry.prototype.toObject>[];
            error?: string;
            created_at?: number;
            completed_at?: number;
        }): StatusResponse {
            const message = new StatusResponse({});
            if (data.request_id != null) {
                message.request_id = data.request_id;
            }
            if (data.status != null) {
                message.status = data.status;
            }
            if (data.status_code != null) {
                message.status_code = data.status_code;
            }
            if (data.body != null) {
                message.body = data.body;
            }
            if (data.headers != null) {
                message.headers = data.headers.map(item => StatusResponse.HeadersEntry.fromObject(item));
            }
            if (data.error != null) {
                message.error = data.error;
            }
            if (data.created_at != null) {
                message.created_at = data.created_at;
            }
            if (data.completed_at != null) {
                message.completed_at = data.completed_at;
            }
            return message;
        }
        toObject() {
            const data: {
                request_id?: string;
                status?: string;
                status_code?: number;
                body?: Uint8Array;
                headers?: ReturnType<typeof StatusResponse.HeadersEntry.prototype.toObject>[];
                error?: string;
                created_at?: number;
                completed_at?: number;
            } = {};
            if (this.request_id != null) {
                data.request_id = this.request_id;
            }
            if (this.status != null) {
                data.status = this.status;
            }
            if (this.status_code != null) {
                data.status_code = this.status_code;
            }
            if (this.body != null) {
                data.body = this.body;
            }
            if (this.headers != null) {
                data.headers = this.headers.map((item: StatusResponse.HeadersEntry) => item.toObject());
            }
            if (this.error != null) {
                data.error = this.error;
            }
            if (this.created_at != null) {
                data.created_at = this.created_at;
            }
            if (this.completed_at != null) {
                data.completed_at = this.completed_at;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.request_id.length)
                writer.writeString(1, this.request_id);
            if (this.status.length)
                writer.writeString(2, this.status);
            if (this.status_code != 0)
                writer.writeInt32(3, this.status_code);
            if (this.body.length)
                writer.writeBytes(4, this.body);
            if (this.headers.length)
                writer.writeRepeatedMessage(5, this.headers, (item: StatusResponse.HeadersEntry) => item.serialize(writer));
            if (this.error.length)
                writer.writeString(6, this.error);
            if (this.created_at != 0)
                writer.writeInt64(7, this.created_at);
            if (this.completed_at != 0)
                writer.writeInt64(8, this.completed_at);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): StatusResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new StatusResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.request_id = reader.readString();
                        break;
                    case 2:
                        message.status = reader.readString();
                        break;
                    case 3:
                        message.status_code = reader.readInt32();
                        break;
                    case 4:
                        message.body = reader.readBytes();
                        break;
                    case 5:
                        reader.readMessage(message.headers, () => pb_1.Message.addToRepeatedWrapperField(message, 5, StatusResponse.HeadersEntry.deserialize(reader), StatusResponse.HeadersEntry));
                        break;
                    case 6:
                        message.error = reader.readString();
                        break;
                    case 7:
                        message.created_at = reader.readInt64();
                        break;
                    case 8:
                        message.completed_at = reader.readInt64();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): StatusResponse {
            return StatusResponse.deserialize(bytes);
        }
    }
    export namespace StatusResponse {
        export class HeadersEntry extends pb_1.Message {
            #one_of_decls: number[][] = [];
            constructor(data?: any[] | {
                key?: string;
                value?: string;
            }) {
                super();
                pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
                if (!Array.isArray(data) && typeof data == "object") {
                    if ("key" in data && data.key != undefined) {
                        this.key = data.key;
                    }
                    if ("value" in data && data.value != undefined) {
                        this.value = data.value;
                    }
                }
            }
            get key() {
                return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
            }
            set key(value: string) {
                pb_1.Message.setField(this, 1, value);
            }
            get value() {
                return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
            }
            set value(value: string) {
                pb_1.Message.setField(this, 2, value);
            }
            static fromObject(data: {
                key?: string;
                value?: string;
            }): HeadersEntry {
                const message = new HeadersEntry({});
                if (data.key != null) {
                    message.key = data.key;
                }
                if (data.value != null) {
                    message.value = data.value;
                }
                return message;
            }
            toObject() {
                const data: {
                    key?: string;
                    value?: string;
                } = {};
                if (this.key != null) {
                    data.key = this.key;
                }
                if (this.value != null) {
                    data.value = this.value;
                }
                return data;
            }
            serialize(): Uint8Array;
            serialize(w: pb_1.BinaryWriter): void;
            serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
                const writer = w || new pb_1.BinaryWriter();
                if (this.key.length)
                    writer.writeString(1, this.key);
                if (this.value.length)
                    writer.writeString(2, this.value);
                if (!w)
                    return writer.getResultBuffer();
            }
            static deserialize(bytes: Uint8Array | pb_1.BinaryReader): HeadersEntry {
                const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new HeadersEntry();
                while (reader.nextField()) {
                    if (reader.isEndGroup())
                        break;
                    switch (reader.getFieldNumber()) {
                        case 1:
                            message.key = reader.readString();
                            break;
                        case 2:
                            message.value = reader.readString();
                            break;
                        default: reader.skipField();
                    }
                }
                return message;
            }
            serializeBinary(): Uint8Array {
                return this.serialize();
            }
            static deserializeBinary(bytes: Uint8Array): HeadersEntry {
                return HeadersEntry.deserialize(bytes);
            }
        }
    }
    export class CancelRequestMessage extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            request_id?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("request_id" in data && data.request_id != undefined) {
                    this.request_id = data.request_id;
                }
            }
        }
        get request_id() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set request_id(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        static fromObject(data: {
            request_id?: string;
        }): CancelRequestMessage {
            const message = new CancelRequestMessage({});
            if (data.request_id != null) {
                message.request_id = data.request_id;
            }
            return message;
        }
        toObject() {
            const data: {
                request_id?: string;
            } = {};
            if (this.request_id != null) {
                data.request_id = this.request_id;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.request_id.length)
                writer.writeString(1, this.request_id);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): CancelRequestMessage {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new CancelRequestMessage();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.request_id = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): CancelRequestMessage {
            return CancelRequestMessage.deserialize(bytes);
        }
    }
    export class CancelResponse extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {
            request_id?: string;
            cancelled?: boolean;
            message?: string;
        }) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") {
                if ("request_id" in data && data.request_id != undefined) {
                    this.request_id = data.request_id;
                }
                if ("cancelled" in data && data.cancelled != undefined) {
                    this.cancelled = data.cancelled;
                }
                if ("message" in data && data.message != undefined) {
                    this.message = data.message;
                }
            }
        }
        get request_id() {
            return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
        }
        set request_id(value: string) {
            pb_1.Message.setField(this, 1, value);
        }
        get cancelled() {
            return pb_1.Message.getFieldWithDefault(this, 2, false) as boolean;
        }
        set cancelled(value: boolean) {
            pb_1.Message.setField(this, 2, value);
        }
        get message() {
            return pb_1.Message.getFieldWithDefault(this, 3, "") as string;
        }
        set message(value: string) {
            pb_1.Message.setField(this, 3, value);
        }
        static fromObject(data: {
            request_id?: string;
            cancelled?: boolean;
            message?: string;
        }): CancelResponse {
            const message = new CancelResponse({});
            if (data.request_id != null) {
                message.request_id = data.request_id;
            }
            if (data.cancelled != null) {
                message.cancelled = data.cancelled;
            }
            if (data.message != null) {
                message.message = data.message;
            }
            return message;
        }
        toObject() {
            const data: {
                request_id?: string;
                cancelled?: boolean;
                message?: string;
            } = {};
            if (this.request_id != null) {
                data.request_id = this.request_id;
            }
            if (this.cancelled != null) {
                data.cancelled = this.cancelled;
            }
            if (this.message != null) {
                data.message = this.message;
            }
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (this.request_id.length)
                writer.writeString(1, this.request_id);
            if (this.cancelled != false)
                writer.writeBool(2, this.cancelled);
            if (this.message.length)
                writer.writeString(3, this.message);
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): CancelResponse {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new CancelResponse();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    case 1:
                        message.request_id = reader.readString();
                        break;
                    case 2:
                        message.cancelled = reader.readBool();
                        break;
                    case 3:
                        message.message = reader.readString();
                        break;
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): CancelResponse {
            return CancelResponse.deserialize(bytes);
        }
    }
    interface GrpcUnaryServiceInterface<P, R> {
        (message: P, metadata: grpc_1.Metadata, options: grpc_1.CallOptions, callback: grpc_1.requestCallback<R>): grpc_1.ClientUnaryCall;
        (message: P, metadata: grpc_1.Metadata, callback: grpc_1.requestCallback<R>): grpc_1.ClientUnaryCall;
        (message: P, options: grpc_1.CallOptions, callback: grpc_1.requestCallback<R>): grpc_1.ClientUnaryCall;
        (message: P, callback: grpc_1.requestCallback<R>): grpc_1.ClientUnaryCall;
    }
    interface GrpcStreamServiceInterface<P, R> {
        (message: P, metadata: grpc_1.Metadata, options?: grpc_1.CallOptions): grpc_1.ClientReadableStream<R>;
        (message: P, options?: grpc_1.CallOptions): grpc_1.ClientReadableStream<R>;
    }
    interface GrpWritableServiceInterface<P, R> {
        (metadata: grpc_1.Metadata, options: grpc_1.CallOptions, callback: grpc_1.requestCallback<R>): grpc_1.ClientWritableStream<P>;
        (metadata: grpc_1.Metadata, callback: grpc_1.requestCallback<R>): grpc_1.ClientWritableStream<P>;
        (options: grpc_1.CallOptions, callback: grpc_1.requestCallback<R>): grpc_1.ClientWritableStream<P>;
        (callback: grpc_1.requestCallback<R>): grpc_1.ClientWritableStream<P>;
    }
    interface GrpcChunkServiceInterface<P, R> {
        (metadata: grpc_1.Metadata, options?: grpc_1.CallOptions): grpc_1.ClientDuplexStream<P, R>;
        (options?: grpc_1.CallOptions): grpc_1.ClientDuplexStream<P, R>;
    }
    interface GrpcPromiseServiceInterface<P, R> {
        (message: P, metadata: grpc_1.Metadata, options?: grpc_1.CallOptions): Promise<R>;
        (message: P, options?: grpc_1.CallOptions): Promise<R>;
    }
    export abstract class UnimplementedEdgeDeviceProxyService {
        static definition = {
            HandleRequest: {
                path: "/edge.v1.EdgeDeviceProxy/HandleRequest",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: DeviceRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => DeviceRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: DeviceResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => DeviceResponse.deserialize(new Uint8Array(bytes))
            },
            EnqueueRequest: {
                path: "/edge.v1.EdgeDeviceProxy/EnqueueRequest",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: AsyncRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => AsyncRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: AsyncResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => AsyncResponse.deserialize(new Uint8Array(bytes))
            },
            GetRequestStatus: {
                path: "/edge.v1.EdgeDeviceProxy/GetRequestStatus",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: StatusRequest) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => StatusRequest.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: StatusResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => StatusResponse.deserialize(new Uint8Array(bytes))
            },
            CancelRequest: {
                path: "/edge.v1.EdgeDeviceProxy/CancelRequest",
                requestStream: false,
                responseStream: false,
                requestSerialize: (message: CancelRequestMessage) => Buffer.from(message.serialize()),
                requestDeserialize: (bytes: Buffer) => CancelRequestMessage.deserialize(new Uint8Array(bytes)),
                responseSerialize: (message: CancelResponse) => Buffer.from(message.serialize()),
                responseDeserialize: (bytes: Buffer) => CancelResponse.deserialize(new Uint8Array(bytes))
            }
        };
        [method: string]: grpc_1.UntypedHandleCall;
        abstract HandleRequest(call: grpc_1.ServerUnaryCall<DeviceRequest, DeviceResponse>, callback: grpc_1.sendUnaryData<DeviceResponse>): void;
        abstract EnqueueRequest(call: grpc_1.ServerUnaryCall<AsyncRequest, AsyncResponse>, callback: grpc_1.sendUnaryData<AsyncResponse>): void;
        abstract GetRequestStatus(call: grpc_1.ServerUnaryCall<StatusRequest, StatusResponse>, callback: grpc_1.sendUnaryData<StatusResponse>): void;
        abstract CancelRequest(call: grpc_1.ServerUnaryCall<CancelRequestMessage, CancelResponse>, callback: grpc_1.sendUnaryData<CancelResponse>): void;
    }
    export class EdgeDeviceProxyClient extends grpc_1.makeGenericClientConstructor(UnimplementedEdgeDeviceProxyService.definition, "EdgeDeviceProxy", {}) {
        constructor(address: string, credentials: grpc_1.ChannelCredentials, options?: Partial<grpc_1.ChannelOptions>) {
            super(address, credentials, options);
        }
        HandleRequest: GrpcUnaryServiceInterface<DeviceRequest, DeviceResponse> = (message: DeviceRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<DeviceResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<DeviceResponse>, callback?: grpc_1.requestCallback<DeviceResponse>): grpc_1.ClientUnaryCall => {
            return super.HandleRequest(message, metadata, options, callback);
        };
        EnqueueRequest: GrpcUnaryServiceInterface<AsyncRequest, AsyncResponse> = (message: AsyncRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<AsyncResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<AsyncResponse>, callback?: grpc_1.requestCallback<AsyncResponse>): grpc_1.ClientUnaryCall => {
            return super.EnqueueRequest(message, metadata, options, callback);
        };
        GetRequestStatus: GrpcUnaryServiceInterface<StatusRequest, StatusResponse> = (message: StatusRequest, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<StatusResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<StatusResponse>, callback?: grpc_1.requestCallback<StatusResponse>): grpc_1.ClientUnaryCall => {
            return super.GetRequestStatus(message, metadata, options, callback);
        };
        CancelRequest: GrpcUnaryServiceInterface<CancelRequestMessage, CancelResponse> = (message: CancelRequestMessage, metadata: grpc_1.Metadata | grpc_1.CallOptions | grpc_1.requestCallback<CancelResponse>, options?: grpc_1.CallOptions | grpc_1.requestCallback<CancelResponse>, callback?: grpc_1.requestCallback<CancelResponse>): grpc_1.ClientUnaryCall => {
            return super.CancelRequest(message, metadata, options, callback);
        };
    }
}
