/**
 * Generated by the protoc-gen-ts.  DO NOT EDIT!
 * compiler version: 3.21.12
 * source: permissionservice.proto
 * git: https://github.com/thesayyn/protoc-gen-ts */
import * as pb_1 from "google-protobuf";
export namespace auth {
    export class auth extends pb_1.Message {
        #one_of_decls: number[][] = [];
        constructor(data?: any[] | {}) {
            super();
            pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
            if (!Array.isArray(data) && typeof data == "object") { }
        }
        static fromObject(data: {}): auth {
            const message = new auth({});
            return message;
        }
        toObject() {
            const data: {} = {};
            return data;
        }
        serialize(): Uint8Array;
        serialize(w: pb_1.BinaryWriter): void;
        serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
            const writer = w || new pb_1.BinaryWriter();
            if (!w)
                return writer.getResultBuffer();
        }
        static deserialize(bytes: Uint8Array | pb_1.BinaryReader): auth {
            const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new auth();
            while (reader.nextField()) {
                if (reader.isEndGroup())
                    break;
                switch (reader.getFieldNumber()) {
                    default: reader.skipField();
                }
            }
            return message;
        }
        serializeBinary(): Uint8Array {
            return this.serialize();
        }
        static deserializeBinary(bytes: Uint8Array): auth {
            return auth.deserialize(bytes);
        }
    }
    export namespace auth {
        export class ValidateUserPermissionRequest extends pb_1.Message {
            #one_of_decls: number[][] = [];
            constructor(data?: any[] | {
                user_id?: string;
                feature_key?: string;
                operation?: number;
            }) {
                super();
                pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
                if (!Array.isArray(data) && typeof data == "object") {
                    if ("user_id" in data && data.user_id != undefined) {
                        this.user_id = data.user_id;
                    }
                    if ("feature_key" in data && data.feature_key != undefined) {
                        this.feature_key = data.feature_key;
                    }
                    if ("operation" in data && data.operation != undefined) {
                        this.operation = data.operation;
                    }
                }
            }
            get user_id() {
                return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
            }
            set user_id(value: string) {
                pb_1.Message.setField(this, 1, value);
            }
            get feature_key() {
                return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
            }
            set feature_key(value: string) {
                pb_1.Message.setField(this, 2, value);
            }
            get operation() {
                return pb_1.Message.getFieldWithDefault(this, 3, 0) as number;
            }
            set operation(value: number) {
                pb_1.Message.setField(this, 3, value);
            }
            static fromObject(data: {
                user_id?: string;
                feature_key?: string;
                operation?: number;
            }): ValidateUserPermissionRequest {
                const message = new ValidateUserPermissionRequest({});
                if (data.user_id != null) {
                    message.user_id = data.user_id;
                }
                if (data.feature_key != null) {
                    message.feature_key = data.feature_key;
                }
                if (data.operation != null) {
                    message.operation = data.operation;
                }
                return message;
            }
            toObject() {
                const data: {
                    user_id?: string;
                    feature_key?: string;
                    operation?: number;
                } = {};
                if (this.user_id != null) {
                    data.user_id = this.user_id;
                }
                if (this.feature_key != null) {
                    data.feature_key = this.feature_key;
                }
                if (this.operation != null) {
                    data.operation = this.operation;
                }
                return data;
            }
            serialize(): Uint8Array;
            serialize(w: pb_1.BinaryWriter): void;
            serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
                const writer = w || new pb_1.BinaryWriter();
                if (this.user_id.length)
                    writer.writeString(1, this.user_id);
                if (this.feature_key.length)
                    writer.writeString(2, this.feature_key);
                if (this.operation != 0)
                    writer.writeInt32(3, this.operation);
                if (!w)
                    return writer.getResultBuffer();
            }
            static deserialize(bytes: Uint8Array | pb_1.BinaryReader): ValidateUserPermissionRequest {
                const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new ValidateUserPermissionRequest();
                while (reader.nextField()) {
                    if (reader.isEndGroup())
                        break;
                    switch (reader.getFieldNumber()) {
                        case 1:
                            message.user_id = reader.readString();
                            break;
                        case 2:
                            message.feature_key = reader.readString();
                            break;
                        case 3:
                            message.operation = reader.readInt32();
                            break;
                        default: reader.skipField();
                    }
                }
                return message;
            }
            serializeBinary(): Uint8Array {
                return this.serialize();
            }
            static deserializeBinary(bytes: Uint8Array): ValidateUserPermissionRequest {
                return ValidateUserPermissionRequest.deserialize(bytes);
            }
        }
        export class ValidatePermissionResponse extends pb_1.Message {
            #one_of_decls: number[][] = [];
            constructor(data?: any[] | {
                has_permission?: boolean;
                error_message?: string;
            }) {
                super();
                pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
                if (!Array.isArray(data) && typeof data == "object") {
                    if ("has_permission" in data && data.has_permission != undefined) {
                        this.has_permission = data.has_permission;
                    }
                    if ("error_message" in data && data.error_message != undefined) {
                        this.error_message = data.error_message;
                    }
                }
            }
            get has_permission() {
                return pb_1.Message.getFieldWithDefault(this, 1, false) as boolean;
            }
            set has_permission(value: boolean) {
                pb_1.Message.setField(this, 1, value);
            }
            get error_message() {
                return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
            }
            set error_message(value: string) {
                pb_1.Message.setField(this, 2, value);
            }
            static fromObject(data: {
                has_permission?: boolean;
                error_message?: string;
            }): ValidatePermissionResponse {
                const message = new ValidatePermissionResponse({});
                if (data.has_permission != null) {
                    message.has_permission = data.has_permission;
                }
                if (data.error_message != null) {
                    message.error_message = data.error_message;
                }
                return message;
            }
            toObject() {
                const data: {
                    has_permission?: boolean;
                    error_message?: string;
                } = {};
                if (this.has_permission != null) {
                    data.has_permission = this.has_permission;
                }
                if (this.error_message != null) {
                    data.error_message = this.error_message;
                }
                return data;
            }
            serialize(): Uint8Array;
            serialize(w: pb_1.BinaryWriter): void;
            serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
                const writer = w || new pb_1.BinaryWriter();
                if (this.has_permission != false)
                    writer.writeBool(1, this.has_permission);
                if (this.error_message.length)
                    writer.writeString(2, this.error_message);
                if (!w)
                    return writer.getResultBuffer();
            }
            static deserialize(bytes: Uint8Array | pb_1.BinaryReader): ValidatePermissionResponse {
                const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new ValidatePermissionResponse();
                while (reader.nextField()) {
                    if (reader.isEndGroup())
                        break;
                    switch (reader.getFieldNumber()) {
                        case 1:
                            message.has_permission = reader.readBool();
                            break;
                        case 2:
                            message.error_message = reader.readString();
                            break;
                        default: reader.skipField();
                    }
                }
                return message;
            }
            serializeBinary(): Uint8Array {
                return this.serialize();
            }
            static deserializeBinary(bytes: Uint8Array): ValidatePermissionResponse {
                return ValidatePermissionResponse.deserialize(bytes);
            }
        }
        export class ValidateCompanyAccessRequest extends pb_1.Message {
            #one_of_decls: number[][] = [];
            constructor(data?: any[] | {
                user_id?: string;
                company_id?: string;
            }) {
                super();
                pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
                if (!Array.isArray(data) && typeof data == "object") {
                    if ("user_id" in data && data.user_id != undefined) {
                        this.user_id = data.user_id;
                    }
                    if ("company_id" in data && data.company_id != undefined) {
                        this.company_id = data.company_id;
                    }
                }
            }
            get user_id() {
                return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
            }
            set user_id(value: string) {
                pb_1.Message.setField(this, 1, value);
            }
            get company_id() {
                return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
            }
            set company_id(value: string) {
                pb_1.Message.setField(this, 2, value);
            }
            static fromObject(data: {
                user_id?: string;
                company_id?: string;
            }): ValidateCompanyAccessRequest {
                const message = new ValidateCompanyAccessRequest({});
                if (data.user_id != null) {
                    message.user_id = data.user_id;
                }
                if (data.company_id != null) {
                    message.company_id = data.company_id;
                }
                return message;
            }
            toObject() {
                const data: {
                    user_id?: string;
                    company_id?: string;
                } = {};
                if (this.user_id != null) {
                    data.user_id = this.user_id;
                }
                if (this.company_id != null) {
                    data.company_id = this.company_id;
                }
                return data;
            }
            serialize(): Uint8Array;
            serialize(w: pb_1.BinaryWriter): void;
            serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
                const writer = w || new pb_1.BinaryWriter();
                if (this.user_id.length)
                    writer.writeString(1, this.user_id);
                if (this.company_id.length)
                    writer.writeString(2, this.company_id);
                if (!w)
                    return writer.getResultBuffer();
            }
            static deserialize(bytes: Uint8Array | pb_1.BinaryReader): ValidateCompanyAccessRequest {
                const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new ValidateCompanyAccessRequest();
                while (reader.nextField()) {
                    if (reader.isEndGroup())
                        break;
                    switch (reader.getFieldNumber()) {
                        case 1:
                            message.user_id = reader.readString();
                            break;
                        case 2:
                            message.company_id = reader.readString();
                            break;
                        default: reader.skipField();
                    }
                }
                return message;
            }
            serializeBinary(): Uint8Array {
                return this.serialize();
            }
            static deserializeBinary(bytes: Uint8Array): ValidateCompanyAccessRequest {
                return ValidateCompanyAccessRequest.deserialize(bytes);
            }
        }
        export class ValidateEnterpriseAccessRequest extends pb_1.Message {
            #one_of_decls: number[][] = [];
            constructor(data?: any[] | {
                user_id?: string;
                enterprise_id?: string;
            }) {
                super();
                pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
                if (!Array.isArray(data) && typeof data == "object") {
                    if ("user_id" in data && data.user_id != undefined) {
                        this.user_id = data.user_id;
                    }
                    if ("enterprise_id" in data && data.enterprise_id != undefined) {
                        this.enterprise_id = data.enterprise_id;
                    }
                }
            }
            get user_id() {
                return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
            }
            set user_id(value: string) {
                pb_1.Message.setField(this, 1, value);
            }
            get enterprise_id() {
                return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
            }
            set enterprise_id(value: string) {
                pb_1.Message.setField(this, 2, value);
            }
            static fromObject(data: {
                user_id?: string;
                enterprise_id?: string;
            }): ValidateEnterpriseAccessRequest {
                const message = new ValidateEnterpriseAccessRequest({});
                if (data.user_id != null) {
                    message.user_id = data.user_id;
                }
                if (data.enterprise_id != null) {
                    message.enterprise_id = data.enterprise_id;
                }
                return message;
            }
            toObject() {
                const data: {
                    user_id?: string;
                    enterprise_id?: string;
                } = {};
                if (this.user_id != null) {
                    data.user_id = this.user_id;
                }
                if (this.enterprise_id != null) {
                    data.enterprise_id = this.enterprise_id;
                }
                return data;
            }
            serialize(): Uint8Array;
            serialize(w: pb_1.BinaryWriter): void;
            serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
                const writer = w || new pb_1.BinaryWriter();
                if (this.user_id.length)
                    writer.writeString(1, this.user_id);
                if (this.enterprise_id.length)
                    writer.writeString(2, this.enterprise_id);
                if (!w)
                    return writer.getResultBuffer();
            }
            static deserialize(bytes: Uint8Array | pb_1.BinaryReader): ValidateEnterpriseAccessRequest {
                const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new ValidateEnterpriseAccessRequest();
                while (reader.nextField()) {
                    if (reader.isEndGroup())
                        break;
                    switch (reader.getFieldNumber()) {
                        case 1:
                            message.user_id = reader.readString();
                            break;
                        case 2:
                            message.enterprise_id = reader.readString();
                            break;
                        default: reader.skipField();
                    }
                }
                return message;
            }
            serializeBinary(): Uint8Array {
                return this.serialize();
            }
            static deserializeBinary(bytes: Uint8Array): ValidateEnterpriseAccessRequest {
                return ValidateEnterpriseAccessRequest.deserialize(bytes);
            }
        }
        export class ValidateAccessResponse extends pb_1.Message {
            #one_of_decls: number[][] = [];
            constructor(data?: any[] | {
                has_access?: boolean;
                error_message?: string;
            }) {
                super();
                pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
                if (!Array.isArray(data) && typeof data == "object") {
                    if ("has_access" in data && data.has_access != undefined) {
                        this.has_access = data.has_access;
                    }
                    if ("error_message" in data && data.error_message != undefined) {
                        this.error_message = data.error_message;
                    }
                }
            }
            get has_access() {
                return pb_1.Message.getFieldWithDefault(this, 1, false) as boolean;
            }
            set has_access(value: boolean) {
                pb_1.Message.setField(this, 1, value);
            }
            get error_message() {
                return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
            }
            set error_message(value: string) {
                pb_1.Message.setField(this, 2, value);
            }
            static fromObject(data: {
                has_access?: boolean;
                error_message?: string;
            }): ValidateAccessResponse {
                const message = new ValidateAccessResponse({});
                if (data.has_access != null) {
                    message.has_access = data.has_access;
                }
                if (data.error_message != null) {
                    message.error_message = data.error_message;
                }
                return message;
            }
            toObject() {
                const data: {
                    has_access?: boolean;
                    error_message?: string;
                } = {};
                if (this.has_access != null) {
                    data.has_access = this.has_access;
                }
                if (this.error_message != null) {
                    data.error_message = this.error_message;
                }
                return data;
            }
            serialize(): Uint8Array;
            serialize(w: pb_1.BinaryWriter): void;
            serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
                const writer = w || new pb_1.BinaryWriter();
                if (this.has_access != false)
                    writer.writeBool(1, this.has_access);
                if (this.error_message.length)
                    writer.writeString(2, this.error_message);
                if (!w)
                    return writer.getResultBuffer();
            }
            static deserialize(bytes: Uint8Array | pb_1.BinaryReader): ValidateAccessResponse {
                const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new ValidateAccessResponse();
                while (reader.nextField()) {
                    if (reader.isEndGroup())
                        break;
                    switch (reader.getFieldNumber()) {
                        case 1:
                            message.has_access = reader.readBool();
                            break;
                        case 2:
                            message.error_message = reader.readString();
                            break;
                        default: reader.skipField();
                    }
                }
                return message;
            }
            serializeBinary(): Uint8Array {
                return this.serialize();
            }
            static deserializeBinary(bytes: Uint8Array): ValidateAccessResponse {
                return ValidateAccessResponse.deserialize(bytes);
            }
        }
        export class UserIdInput extends pb_1.Message {
            #one_of_decls: number[][] = [];
            constructor(data?: any[] | {
                user_id?: string;
            }) {
                super();
                pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
                if (!Array.isArray(data) && typeof data == "object") {
                    if ("user_id" in data && data.user_id != undefined) {
                        this.user_id = data.user_id;
                    }
                }
            }
            get user_id() {
                return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
            }
            set user_id(value: string) {
                pb_1.Message.setField(this, 1, value);
            }
            static fromObject(data: {
                user_id?: string;
            }): UserIdInput {
                const message = new UserIdInput({});
                if (data.user_id != null) {
                    message.user_id = data.user_id;
                }
                return message;
            }
            toObject() {
                const data: {
                    user_id?: string;
                } = {};
                if (this.user_id != null) {
                    data.user_id = this.user_id;
                }
                return data;
            }
            serialize(): Uint8Array;
            serialize(w: pb_1.BinaryWriter): void;
            serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
                const writer = w || new pb_1.BinaryWriter();
                if (this.user_id.length)
                    writer.writeString(1, this.user_id);
                if (!w)
                    return writer.getResultBuffer();
            }
            static deserialize(bytes: Uint8Array | pb_1.BinaryReader): UserIdInput {
                const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new UserIdInput();
                while (reader.nextField()) {
                    if (reader.isEndGroup())
                        break;
                    switch (reader.getFieldNumber()) {
                        case 1:
                            message.user_id = reader.readString();
                            break;
                        default: reader.skipField();
                    }
                }
                return message;
            }
            serializeBinary(): Uint8Array {
                return this.serialize();
            }
            static deserializeBinary(bytes: Uint8Array): UserIdInput {
                return UserIdInput.deserialize(bytes);
            }
        }
        export class ListCompanyOrEnterpriseResponse extends pb_1.Message {
            #one_of_decls: number[][] = [];
            constructor(data?: any[] | {
                ids?: string[];
                is_all?: boolean;
            }) {
                super();
                pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [1], this.#one_of_decls);
                if (!Array.isArray(data) && typeof data == "object") {
                    if ("ids" in data && data.ids != undefined) {
                        this.ids = data.ids;
                    }
                    if ("is_all" in data && data.is_all != undefined) {
                        this.is_all = data.is_all;
                    }
                }
            }
            get ids() {
                return pb_1.Message.getFieldWithDefault(this, 1, []) as string[];
            }
            set ids(value: string[]) {
                pb_1.Message.setField(this, 1, value);
            }
            get is_all() {
                return pb_1.Message.getFieldWithDefault(this, 2, false) as boolean;
            }
            set is_all(value: boolean) {
                pb_1.Message.setField(this, 2, value);
            }
            static fromObject(data: {
                ids?: string[];
                is_all?: boolean;
            }): ListCompanyOrEnterpriseResponse {
                const message = new ListCompanyOrEnterpriseResponse({});
                if (data.ids != null) {
                    message.ids = data.ids;
                }
                if (data.is_all != null) {
                    message.is_all = data.is_all;
                }
                return message;
            }
            toObject() {
                const data: {
                    ids?: string[];
                    is_all?: boolean;
                } = {};
                if (this.ids != null) {
                    data.ids = this.ids;
                }
                if (this.is_all != null) {
                    data.is_all = this.is_all;
                }
                return data;
            }
            serialize(): Uint8Array;
            serialize(w: pb_1.BinaryWriter): void;
            serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
                const writer = w || new pb_1.BinaryWriter();
                if (this.ids.length)
                    writer.writeRepeatedString(1, this.ids);
                if (this.is_all != false)
                    writer.writeBool(2, this.is_all);
                if (!w)
                    return writer.getResultBuffer();
            }
            static deserialize(bytes: Uint8Array | pb_1.BinaryReader): ListCompanyOrEnterpriseResponse {
                const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes), message = new ListCompanyOrEnterpriseResponse();
                while (reader.nextField()) {
                    if (reader.isEndGroup())
                        break;
                    switch (reader.getFieldNumber()) {
                        case 1:
                            pb_1.Message.addToRepeatedField(message, 1, reader.readString());
                            break;
                        case 2:
                            message.is_all = reader.readBool();
                            break;
                        default: reader.skipField();
                    }
                }
                return message;
            }
            serializeBinary(): Uint8Array {
                return this.serialize();
            }
            static deserializeBinary(bytes: Uint8Array): ListCompanyOrEnterpriseResponse {
                return ListCompanyOrEnterpriseResponse.deserialize(bytes);
            }
        }
    }
}
