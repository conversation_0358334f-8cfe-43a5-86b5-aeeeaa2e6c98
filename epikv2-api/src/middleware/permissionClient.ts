import { config } from '@/config/environment';
import {
  AppError,
  ForbiddenError,
  UnauthorizedError,
  ValidationError,
} from '@/middleware/errorHandler';
import { Features, ListAccessResponse } from '@/types';
import { createModuleLogger } from '@/utils/logger';
import { GrpcServiceClient, safeGrpcOperation } from '@/utils/grpcClient';
import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';
import fp from 'fastify-plugin';

export class DynamicPermissionClient extends GrpcServiceClient {
  constructor(serverAddress?: string) {
    super({
      serverAddress: serverAddress || config.authGrpcUri,
      serviceName: 'PermissionService',
      packageName: 'auth',
      maxRetries: 3,
      retryDelay: 1000
    });
  }

  async validateUserPermission(
    userId: string,
    featureKey: string,
    operation: number
  ): Promise<any> {
    return this.makeRequest<any>(
      'validateUserPermission',
      { user_id: userId, feature_key: featureKey, operation },
      'has_permission'
    );
  }

  async validateCompanyAccess(userId: string, companyId: string): Promise<Boolean> {
    return this.makeRequest<any>(
      'validateCompanyAccess',
      { user_id: userId, company_id: companyId },
      'has_access'
    );
  }

  async validateEnterpriseAccess(userId: string, enterpriseId: string): Promise<Boolean> {
    return this.makeRequest<any>(
      'validateEnterpriseAccess',
      { user_id: userId, enterprise_id: enterpriseId },
      'has_access'
    );
  }

  async listAllowedCompanies(userId: string): Promise<any> {
    return this.makeRequest<any>('ListUserAccessableCompaniesWithEnterprises', { user_id: userId });
  }

  async listAllowedEnterprises(userId: string): Promise<any> {
    return this.makeRequest<any>('ListUserAccessableEnterprises', { user_id: userId });
  }
}

const permissionClient = new DynamicPermissionClient();
const permLogger = createModuleLogger('permissionClient');

export const permissionPlugin = fp(async function (fastify: FastifyInstance) {
  try {
    await permissionClient.connect();
  } catch (error) {
    fastify.log.warn('Permission service not available, will retry on first request');
  }

  fastify.decorate(
    'requirePermission',
    async (request: FastifyRequest, featureKey: string, operation: number): Promise<Boolean> => {
      const res = await safeGrpcOperation(async () => {
        return await permissionClient.validateUserPermission(
          request.user.id,
          featureKey,
          operation
        );
      }, 'validate permission', permLogger);
      return res;
    }
  );

  fastify.decorate('requirePermissionHook', (featureKey: Features, operation: number) => {
    return async (request: FastifyRequest, _: FastifyReply) => {
      const res = await fastify.requirePermission(request, featureKey, operation);
      if (!res) {
        throw new ForbiddenError(`Missing required permission: ${featureKey}`);
      }
    };
  });

  fastify.decorate(
    'requireCompanyAccess',
    async (request: FastifyRequest, companyIdParam: string = 'companyId'): Promise<Boolean> => {
      const companyId = companyIdParam;

      if (!companyId) {
        return false;
      }

      return await safeGrpcOperation(async () => {
        const hasAccess = await permissionClient.validateCompanyAccess(request.user.id, companyId);
        return hasAccess;
      }, 'validate company access', permLogger);
    }
  );

  fastify.decorate(
    'requireEnterpriseAccess',
    async (
      request: FastifyRequest,
      enterpriseIdParam: string = 'enterpriseId'
    ): Promise<Boolean> => {
      const enterpriseId = enterpriseIdParam;

      if (!enterpriseId) {
        return false;
      }

      return await safeGrpcOperation(async () => {
        const hasAccess = await permissionClient.validateEnterpriseAccess(
          request.user.id,
          enterpriseId
        );
        return hasAccess;
      }, 'validate enterprise access', permLogger);
    }
  );
  fastify.decorate(
    'listAllowedCompanies',
    async (request: FastifyRequest): Promise<ListAccessResponse> => {
      if (!request.user || !request.user.id) {
        throw new UnauthorizedError('Authentication required');
      }

      return await safeGrpcOperation(async () => {
        const allcompanies = await permissionClient.listAllowedCompanies(request.user.id);
        return {
          ids: allcompanies.ids || [],
          isAll: allcompanies.is_all ?? false,
        };
      }, 'list all companies', permLogger);
    }
  );
  fastify.decorate(
    'listAllowedEnterprises',
    async (request: FastifyRequest): Promise<ListAccessResponse> => {
      if (!request.user || !request.user.id) {
        throw new UnauthorizedError('Authentication required');
      }

      return await safeGrpcOperation(async () => {
        const allEnterprises = await permissionClient.listAllowedEnterprises(request.user.id);
        return {
          ids: allEnterprises.ids || [],
          isAll: allEnterprises.is_all ?? false,
        };
      }, 'list all enterprises', permLogger);
    }
  );
});
