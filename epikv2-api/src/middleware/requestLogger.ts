import { apiLogger, performanceLogger } from '@/utils/logger';
import { FastifyReply, FastifyRequest } from 'fastify';
import fp from 'fastify-plugin';

export const requestLogger = fp(async function (fastify) {
  fastify.addHook('onRequest', async (request: FastifyRequest) => {
    const requestId =
      (request.headers['x-request-id'] as string) ||
      `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

    (request as any).startTime = Date.now();
    (request as any).requestId = requestId;

    request.headers['x-request-id'] = requestId;
  });

  fastify.addHook('preHandler', async (request: FastifyRequest) => {
    const logData = {
      requestId: (request as any).requestId,
      method: request.method,
      url: request.url,
      userAgent: request.headers['user-agent'],
      ip: request.ip,
      userId: request.user?.id,
      contentLength: request.headers['content-length'],
      referer: request.headers.referer,
    };

    apiLogger.info(logData, `Incoming ${request.method} ${request.url}`);
  });

  fastify.addHook('onSend', async (request: FastifyRequest, reply: FastifyReply, payload) => {
    // If we've already seen this request in the error handler, and this is a subsequent onSend call
    if ((request as any)._onSendCalled) {
      return payload;
    }
    
    (request as any)._onSendCalled = true;

    const duration = Date.now() - (request as any).startTime;
    const requestId = (request as any).requestId;

    reply.header('x-request-id', requestId);

    const logData = {
      requestId,
      method: request.method,
      url: request.url,
      statusCode: reply.statusCode,
      duration,
      contentLength: reply.getHeader('content-length'),
      userId: request.user?.id,
      ip: request.ip,
      userAgent: request.headers['user-agent'],
    };
    if (reply.statusCode >= 500) {
      apiLogger.error(logData, `Request failed: ${request.method} ${request.url}`);
    } else if (reply.statusCode >= 400) {
      apiLogger.warn(logData, `Client error: ${request.method} ${request.url}`);
    } else if (duration > 1000) {
      performanceLogger.warn(logData, `Slow request: ${request.method} ${request.url}`);
    } else {
      apiLogger.info(logData, `Request completed: ${request.method} ${request.url}`);
    }

    trackPerformanceMetrics(request, reply, duration);

    return payload;
  });

  fastify.addHook('onError', async (request: FastifyRequest, reply: FastifyReply, error) => {
    const duration = Date.now() - (request as any).startTime;
    const requestId = (request as any).requestId;

    const logData = {
      requestId,
      method: request.method,
      url: request.url,
      duration,
      error: {
        name: error.name,
        message: error.message,
        code: (error as any).code,
      },
      userId: request.user?.id,
      ip: request.ip,
    };

    apiLogger.error(logData, `Request error: ${request.method} ${request.url}`);
  });
});

function trackPerformanceMetrics(
  request: FastifyRequest,
  reply: FastifyReply,
  duration: number
): void {
  const route = `${request.method} ${request.routeOptions?.url || request.url}`;

  performanceLogger.debug(
    {
      route,
      duration,
      statusCode: reply.statusCode,
      method: request.method,
    },
    'Route performance'
  );

  if (duration > 5000) {
    performanceLogger.error(
      {
        route,
        duration,
        requestId: (request as any).requestId,
        userId: request.user?.id,
      },
      'Very slow request detected'
    );
  }

  if (duration > 2000) {
    const memUsage = process.memoryUsage();
    performanceLogger.warn(
      {
        route,
        duration,
        memory: {
          rss: Math.round(memUsage.rss / 1024 / 1024),
          heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
          heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
          external: Math.round(memUsage.external / 1024 / 1024),
        },
      },
      'Memory usage for slow request'
    );
  }
}

export const correlationMiddleware = fp(async function (fastify) {
  fastify.addHook('onRequest', async (request: FastifyRequest, reply: FastifyReply) => {
    const correlationId =
      (request.headers['x-correlation-id'] as string) ||
      (request.headers['x-request-id'] as string) ||
      `corr_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

    (request as any).correlationId = correlationId;

    reply.header('x-correlation-id', correlationId);
  });
});

export const securityHeadersMiddleware = fp(async function (fastify) {
  fastify.addHook('onSend', async (request: FastifyRequest, reply: FastifyReply, payload) => {
    reply.header('x-content-type-options', 'nosniff');
    reply.header('x-frame-options', 'DENY');
    reply.header('x-xss-protection', '1; mode=block');
    reply.header('referrer-policy', 'strict-origin-when-cross-origin');

    if (request.url.startsWith('/api/')) {
      reply.header('cache-control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
      reply.header('pragma', 'no-cache');
      reply.header('expires', '0');
    }

    return payload;
  });
});
