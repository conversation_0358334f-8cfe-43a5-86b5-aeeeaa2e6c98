import { GraphqlContext } from '@/types';
import { Features, ValidateOperations } from '@/types';
import { AuthChecker } from 'type-graphql';

export const customAuthChecker: AuthChecker<GraphqlContext, [Features, ValidateOperations?]> =async (
  { context },
  roles
):Promise<boolean> => {
  if (!context.user) {
    return false;
  }
  // If no roles are specified, allow access
  if (roles.length === 0) {
    return true;
  }
  const [feature, operation] = roles[0];
  const res =await context.fastify.requirePermission(
    context.request,
    feature,
    operation || ValidateOperations.ValidateRead
  );
  return res as boolean;
};
