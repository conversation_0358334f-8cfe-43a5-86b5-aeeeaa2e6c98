import { config } from '@/config/environment';
import { ErrorResponseData, JSONResponse } from '@/types';
import { FastifyError, FastifyReply, FastifyRequest } from 'fastify';
import fp from 'fastify-plugin';
import { ZodError } from 'zod';

export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;
  public code: string | undefined;

  constructor(
    message: string,
    statusCode: number = 500,
    isOperational: boolean = true,
    code?: string
  ) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.code = code;

    Error.captureStackTrace(this, this.constructor);
  }
}

export class ValidationError extends AppError {
  constructor(message: string, _details?: unknown) {
    super(message, 400, true, 'VALIDATION_ERROR');
    this.name = 'ValidationError';
  }
}

export class NotFoundError extends AppError {
  constructor(resource: string = 'Resource') {
    super(`${resource} not found`, 404, true, 'NOT_FOUND');
    this.name = 'NotFoundError';
  }
}

export class ConflictError extends AppError {
  constructor(message: string) {
    super(message, 409, true, 'CONFLICT');
    this.name = 'ConflictError';
  }
}

export class UnauthorizedError extends AppError {
  constructor(message: string = 'Unauthorized') {
    super(message, 401, true, 'UNAUTHORIZED');
    this.name = 'UnauthorizedError';
  }
}

export class ForbiddenError extends AppError {
  constructor(message: string = 'Forbidden') {
    super(message, 403, true, 'FORBIDDEN');
    this.name = 'ForbiddenError';
  }
}

export class RateLimitError extends AppError {
  constructor(message: string = 'Too many requests') {
    super(message, 429, true, 'RATE_LIMIT');
    this.name = 'RateLimitError';
  }
}

function formatZodError(error: ZodError): { message: string; details: unknown } {
  const details = error.errors.map(err => ({
    field: err.path.join('.'),
    message: err.message,
    code: err.code,
  }));

  return {
    message: 'Validation failed',
    details,
  };
}

function formatMongoError(error: any): { message: string; statusCode: number; code?: string } {
  if (error.code === 11000) {
    const field = Object.keys(error.keyPattern || {})[0] || 'field';
    return {
      message: `Duplicate value for ${field}`,
      statusCode: 409,
      code: 'DUPLICATE_KEY',
    };
  }

  if (error.name === 'ValidationError') {
    return {
      message: 'Database validation failed',
      statusCode: 400,
      code: 'DB_VALIDATION_ERROR',
    };
  }

  return {
    message: 'Database operation failed',
    statusCode: 500,
    code: 'DB_ERROR',
  };
}

// Main error handler
export const errorHandler = fp(async function (fastify) {
  fastify.setErrorHandler(
    async (error: FastifyError, request: FastifyRequest, reply: FastifyReply) => {
      // Mark that we're handling an error to prevent double onSend
      (request as any)._errorHandled = true;
      
      const startTime = Date.now();
      const requestId =
        (request.headers['x-request-id'] as string) ||
        request.id ||
        `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

      let statusCode = 500;
      let message = 'Internal Server Error';
      let code: string | undefined;
      let details: unknown;
      if (error instanceof AppError) {
        statusCode = error.statusCode;
        message = error.message;
        code = error.code;
      } else if (error instanceof ZodError) {
        statusCode = 400;
        const formatted = formatZodError(error);
        message = formatted.message;
        details = formatted.details;
        code = 'VALIDATION_ERROR';
      } else if (error.name === 'MongoError' || error.name === 'MongoServerError') {
        const formatted = formatMongoError(error);
        statusCode = formatted.statusCode;
        message = formatted.message;
        code = formatted.code;
      } else if (error.statusCode) {
        statusCode = error.statusCode;
        message = error.message;
        if (error.code === 'FST_ERR_VALIDATION') {
          code = 'VALIDATION_ERROR';
          statusCode = 400;
        } else if (error.code === 'FST_JWT_NO_AUTHORIZATION_IN_HEADER') {
          code = 'UNAUTHORIZED';
          statusCode = 401;
          message = 'Missing authorization header';
        } else if (error.code === 'FST_JWT_AUTHORIZATION_TOKEN_EXPIRED') {
          code = 'TOKEN_EXPIRED';
          statusCode = 401;
          message = 'Token has expired';
        }
      }

      const errorResponseData: ErrorResponseData = {
        message,
        statusCode,
        timestamp: new Date().toISOString(),
        path: request.url,
        requestId,
      };

      if (code) {
        errorResponseData.code = code;
      }

      if (details) {
        errorResponseData.details = details;
      }

      // Log error with appropriate level
      const logContext = {
        requestId,
        method: request.method,
        url: request.url,
        statusCode,
        userAgent: request.headers['user-agent'],
        ip: request.ip,
        userId: request.user?.id,
        duration: Date.now() - startTime,
      };

      if (!config.devMode && statusCode >= 500) {
        errorResponseData.message = 'Internal Server Error';
        delete errorResponseData.details;
      }

      if (!config.devMode) {
        delete (errorResponseData as any).stack;
      } else if (error.stack) {
        (errorResponseData as any).stack = error.stack;
      }

      // Use standardized error response format
      reply.status(statusCode).send({
        status: 'error',
        error: errorResponseData,
      } as JSONResponse<never>);
    }
  );
});
