import { getAllModels, ModelInstanceMap, ObiDocument } from '@/models';
import { ObjectId } from 'mongodb';

export class ObiService {
  models: ModelInstanceMap;

  constructor(models?: ModelInstanceMap) {
    this.models = models ?? getAllModels();
  }

  async listObisByIds(obiIds: ObjectId[]): Promise<ObiDocument[]> {
    return this.models.obis.findMany({ _id: { $in: obiIds } }, {}, obiIds);
  }  

}
