import { ValidationError } from '@/middleware/errorHandler';
import { getAllModels, ModelInstanceMap } from '@/models';
import { EpikBoxDocument, EpikBoxFilterInput } from '@/models/epikbox';
import { ListEpikBoxPaginatedResultType, PaginationInput } from '@/types';
import { createModuleLogger } from '@/utils/logger';
const logger = createModuleLogger('EpikBoxService');

export interface UpdateEpikBoxData {
  serialNumber?: string;
  vpnAddress?: string;
  status?: string;
  lastSeen?: Date;
}

const searchableFields: (keyof EpikBoxFilterInput)[] = [
  'serialNumber',
  'vpnAddress',
  'displayName',
  'imei',
  'sim',
  'status',
  'shipingNumber',
];
const searchableFieldsDBMap: { [key in keyof EpikBoxFilterInput]?: string } = {
  imei: 'modems.imeis',
  sim: 'modems.sims',
  status: 'registered',
  shipingNumber: 'trackingInfo.trackingNumber',
};

export class EpikBoxService {
  models: ModelInstanceMap;

  constructor(models?: ModelInstanceMap) {
    this.models = models ?? getAllModels();
  }
  async listEpikBoxes(
    filter: EpikBoxFilterInput,
    pagination: PaginationInput
  ): Promise<ListEpikBoxPaginatedResultType> {
    const page = pagination?.page || 1;
    const pageSize = pagination?.pageSize || 20;

    const applyRegex = (value?: string) =>
      value?.trim() ? { $regex: value.trim(), $options: 'i' } : undefined;

    const match: Record<string, any> = {};
    const orConditions: any[] = [];

    const hasCompanyFilter = !!filter?.companyName?.trim();
    const trimmedQuery = filter?.query?.trim();

    if (!filter?.isAll) {
      match['assignedTo'] = { $in: filter?.ids || [] };
    }

    if (hasCompanyFilter) {
      //check count of companies with compny filter if greater than 50 ignore this request otherwise get companies id of all companies and add in filter
      const companyRegex = applyRegex(filter.companyName);
      const filterQuery = { name: companyRegex } as any;
      if(!filter?.isAll) {
        filterQuery._id = { $in: filter?.ids || [] };
      }
      const count = await this.models.companiesv2.count(filterQuery);
      if (count > 50) {
        throw new ValidationError('Too many companies to filter');
      }
      const companyIds = await this.models.companiesv2.findMany(filterQuery, { _id: 1 });
      match['assignedTo'] = { $in: companyIds.map((c: any) => c._id) };
    }

    if (filter) {
      for (const key of searchableFields) {
        const value = filter[key];
        const dbKey = searchableFieldsDBMap[key] || key;
  
        if (key === 'status' && typeof value === 'string') {
          if (value === 'registered') {
            match['registered'] = true;
          } else if (value === 'unregistered') {
            match['registered'] = false;
          }
          continue;
        }
    
        const regex = typeof value === 'string' ? applyRegex(value) : value;
        if (regex) match[dbKey] = regex;
      }
    }

    if (trimmedQuery) {
      const queryRegex = applyRegex(trimmedQuery);
      for (const field of searchableFields) {
        const dbKey = searchableFieldsDBMap[field] || field;
        orConditions.push({ [dbKey]: queryRegex });
      }
    }

    if (orConditions.length) {
      match.$or = orConditions;
    }

    const docs = await this.models.epikboxes.findWithPagination(match, pagination);

    return docs;
  }

  async updateEpikBox(id: string, data: UpdateEpikBoxData): Promise<EpikBoxDocument | null> {
    return await this.models.epikboxes.update(id, data);
  }
}
