import { config } from '@/config/environment';
import { GrpcServiceClient } from '@/utils/grpcClient';
import { edge } from '@/types/generated/edgedeviceproxy';

// Re-export the generated types for convenience
export type DeviceRequest = edge.v1.DeviceRequest;
export type DeviceResponse = edge.v1.DeviceResponse;
export type AsyncRequest = edge.v1.AsyncRequest;
export type AsyncResponse = edge.v1.AsyncResponse;
export type StatusRequest = edge.v1.StatusRequest;
export type StatusResponse = edge.v1.StatusResponse;
export type CancelRequestMessage = edge.v1.CancelRequestMessage;
export type CancelResponse = edge.v1.CancelResponse;
export type DashboardRequest = edge.v1.DashboardRequest;
export type DashboardResponse = edge.v1.DashboardResponse;

export class EdgeProxyService extends GrpcServiceClient {
  constructor(serverAddress?: string) {
    super({
      serverAddress: serverAddress || config.edgeProxyUri,
      serviceName: 'EdgeDeviceProxy',
      packageName: 'edge.v1',
      maxRetries: 3,
      retryDelay: 1000,
    });
    // Don't call connect() here - it's handled automatically by the base class
  }

  // Handle synchronous or asynchronous requests to edge devices
  async handleRequest(request: DeviceRequest): Promise<DeviceResponse> {
    return this.makeRequest<DeviceResponse>('HandleRequest', request);
  }

  // Enqueue a request for asynchronous processing
  async enqueueRequest(request: AsyncRequest): Promise<AsyncResponse> {
    return this.makeRequest<AsyncResponse>('EnqueueRequest', request);
  }

  // Get the status of an asynchronous request
  async getRequestStatus(request: StatusRequest): Promise<StatusResponse> {
    return this.makeRequest<StatusResponse>('GetRequestStatus', request);
  }

  // Cancel a pending asynchronous request
  async cancelRequest(request: CancelRequestMessage): Promise<CancelResponse> {
    return this.makeRequest<CancelResponse>('CancelRequest', request);
  }

  // Get dashboard data for a device
  async getDashboardData(request: DashboardRequest): Promise<DashboardResponse> {
    return this.makeRequest<DashboardResponse>('DashboardData', request);
  }

  // Helper function to convert Record<string, string> to HeadersEntry[]
  private convertToHeadersEntries(headers: Record<string, string>): edge.v1.DeviceRequest.HeadersEntry[] {
    return Object.entries(headers).map(([key, value]) =>
      new edge.v1.DeviceRequest.HeadersEntry({ key, value })
    );
  }

  // Helper function to convert Record<string, string> to MetadataEntry[]
  private convertToMetadataEntries(metadata: Record<string, string>): edge.v1.DeviceRequest.MetadataEntry[] {
    return Object.entries(metadata).map(([key, value]) =>
      new edge.v1.DeviceRequest.MetadataEntry({ key, value })
    );
  }

  // Convenience method for simple device requests
  async proxyToDevice(
    serialNumber: string,
    path: string,
    options?: {
      priority?: number;
      async?: boolean;
      headers?: Record<string, string>;
      metadata?: Record<string, string>;
    }
  ): Promise<DeviceResponse> {
    const request = new edge.v1.DeviceRequest({
      serial_number: serialNumber,
      path: path,
      priority: options?.priority || 1,
      async: options?.async || false,
      headers: options?.headers ? this.convertToHeadersEntries(options.headers) : [],
      metadata: options?.metadata ? this.convertToMetadataEntries(options.metadata) : []
    });

    return this.handleRequest(request);
  }
}
