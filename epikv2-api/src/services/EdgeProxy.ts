import { config } from '@/config/environment';
import { GrpcServiceClient } from '@/utils/grpcClient';

// TypeScript interfaces for the proto messages
export interface DeviceRequest {
  serial_number: string;
  path: string;
  priority?: number;
  async?: boolean;
  headers?: Record<string, string>;
  metadata?: Record<string, string>;
}

export interface DeviceResponse {
  status_code: number;
  body: Uint8Array;
  headers: Record<string, string>;
  request_id: string;
  is_async: boolean;
}

export interface AsyncRequest {
  serial_number: string;
  path: string;
  priority?: number;
  headers?: Record<string, string>;
  metadata?: Record<string, string>;
}

export interface AsyncResponse {
  request_id: string;
  status: string;
  estimated_completion_seconds: number;
}

export interface StatusRequest {
  request_id: string;
}

export interface StatusResponse {
  request_id: string;
  status: string;
  status_code: number;
  body: Uint8Array;
  headers: Record<string, string>;
  error: string;
  created_at: number;
  completed_at: number;
}

export interface CancelRequestMessage {
  request_id: string;
}

export interface CancelResponse {
  request_id: string;
  cancelled: boolean;
  message: string;
}

export class EdgeProxyService extends GrpcServiceClient {
  constructor(serverAddress?: string, useProtoCache = true) {
    super({
      serverAddress: serverAddress || config.edgeProxyUri,
      serviceName: 'EdgeDeviceProxy',
      packageName: 'edge.v1',
      maxRetries: 3,
      retryDelay: 1000,
      useProtoFiles: useProtoCache,
      forceRegenerate: false, // Set to true if you want to force regeneration
    });
    // Don't call connect() here - it's handled automatically by the base class
  }

  // Handle synchronous or asynchronous requests to edge devices
  async handleRequest(request: DeviceRequest): Promise<DeviceResponse> {
    return this.makeRequest<DeviceResponse>('HandleRequest', request);
  }

  // Enqueue a request for asynchronous processing
  async enqueueRequest(request: AsyncRequest): Promise<AsyncResponse> {
    return this.makeRequest<AsyncResponse>('EnqueueRequest', request);
  }

  // Get the status of an asynchronous request
  async getRequestStatus(request: StatusRequest): Promise<StatusResponse> {
    return this.makeRequest<StatusResponse>('GetRequestStatus', request);
  }

  // Cancel a pending asynchronous request
  async cancelRequest(request: CancelRequestMessage): Promise<CancelResponse> {
    return this.makeRequest<CancelResponse>('CancelRequest', request);
  }

  // Convenience method for simple device requests
  async proxyToDevice(
    serialNumber: string,
    path: string,
    options?: {
      priority?: number;
      async?: boolean;
      headers?: Record<string, string>;
      metadata?: Record<string, string>;
    }
  ): Promise<DeviceResponse> {
    const request: DeviceRequest = {
      serial_number: serialNumber,
      path: path,
      priority: options?.priority || 1,
      async: options?.async || false,
      headers: options?.headers || {},
      metadata: options?.metadata || {}
    };

    return this.handleRequest(request);
  }
}
