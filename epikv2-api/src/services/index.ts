import { FastifyInstance } from 'fastify';
import { EpikBoxService } from './EpikBoxService';
import { ObiService } from './ObiService';
import fp from 'fastify-plugin';
import { EdgeProxyService } from './EdgeProxy';

export type Services = {
  epikBoxService: EpikBoxService;
  obiService: ObiService;
  edgeProxyService: EdgeProxyService;
};

export const createServices = (fastify: FastifyInstance) => {
  const epikBoxService = new EpikBoxService();
  const obiService = new ObiService();
  const edgeProxyService = new EdgeProxyService();

  const services: Services = {
    epikBoxService,
    obiService,
    edgeProxyService,
  };
  fastify.decorate('services', services);
};

export const servicesPlugin = fp(async function (fastify: FastifyInstance) {
  createServices(fastify);
});
