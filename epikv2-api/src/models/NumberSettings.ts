import { Field, ObjectType } from "type-graphql";
import { NumberDocument } from "./number";

@ObjectType()
export class NumberSettings {
  @Field(() => String, { nullable: true })
  dtmfType?: string;

  @Field(() => String, { nullable: true })
  dnis?: string;

  @Field(() => String, { nullable: true })
  callerIdName?: string;

  @Field(() => String, { nullable: true })
  callerIdNumber?: string;

  @Field(() => [NumberDocument], { nullable: true })
  numbers?: NumberDocument[];
}