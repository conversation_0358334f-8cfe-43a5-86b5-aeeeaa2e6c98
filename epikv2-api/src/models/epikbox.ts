import { BaseDocument } from '@/types/mongodb';
import { FastifyInstance } from 'fastify';
import { BaseModel } from './base.model';
import { ObjectType, Field, ID, InputType } from 'type-graphql';
import { IsOptional, IsString, isBoolean } from 'class-validator';
import { ObjectId } from 'mongodb';
import { CompanyDocument } from './company';
import { LocationDocument } from './location';
import { ObiDocument } from './obi';
import { PRISettings } from './PRISettings';
import { CASSettings } from './CASSettings';
/*
modems:{
          label: '',
          type: 'legacy',
          imeis: imei[1],
          sims: [],
          phones: phones.length === 2 ? [phones[1]] : [],
        }
*/
@ObjectType()
export class EpikBoxDocument implements BaseDocument {
  @Field(() => ID)
  _id?: ObjectId;

  @Field(() => String, { nullable: true }) // nullable should be remove later
  serialNumber: string;

  @Field(() => String, { nullable: true }) // nullable should be remove later
  vpnAddress: string;

  @Field(() => String, { nullable: true })
  status?: string;

  @Field(() => Date, { nullable: true })
  lastSeen?: Date;

  @Field(() => Date, { nullable: true })
  creationDate?: Date;

  @Field(() => Date, { nullable: true })
  lastUpdated?: Date;

  @Field(() => Boolean, { nullable: true })
  deleted?: boolean;

  @Field(() => Boolean, { nullable: true })
  monitor?: boolean;

  @Field(() => String, { nullable: true })
  displayName?: string;

  @Field(() => Number, { nullable: true })
  numPorts?: number;

  @Field(() => ObjectId, { nullable: true })
  assignedTo?: ObjectId;

  @Field(() => CompanyDocument, { nullable: true })
  companyDoc?: CompanyDocument;

  @Field(() => ObjectId, { nullable: true })
  locRef?: ObjectId;

  @Field(() => LocationDocument, { nullable: true })
  locationDoc?: LocationDocument;

  @Field(() => String, { nullable: true })
  lteIp?: string;

  @Field(() => String, { nullable: true })
  customerProvidedIp?: string;

  @Field(() => String, { nullable: true })
  powerState?: string;

  @Field(() => Boolean, { nullable: true })
  registered?: boolean;

  @Field(() => String, { nullable: true })
  activeCarrier?: string;

  @Field(() => String, { nullable: true })
  datacenter?: string;

  @Field(() => String, { nullable: true })
  devicePower?: string;

  @Field(() => String, { nullable: true })
  cpuTemp?: string;

  @Field(() => String, { nullable: true })
  signalStrength?: string;

  @Field(() => PRISettings, { nullable: true })
  priSettings?: PRISettings;

  @Field(() => CASSettings, { nullable: true })
  casSettings?: CASSettings;

  @Field(() => [ObjectId], { nullable: true })
  Obis?: ObjectId[];

  @Field(() => [ObiDocument], { nullable: true })
  obiDocs?: ObiDocument[];
}

// Input Types
@InputType()
export class EpikBoxFilterInput {
  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  query?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  serialNumber?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  vpnAddress?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  displayName?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  company?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  status?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  number?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  macAddress?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  epiNumber?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  shipingNumber?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  imei?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  sim?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  createdon?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  companyName?: string;

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  isAll?: boolean;

  @Field(() => [String], { nullable: true })
  @IsOptional()
  ids?: string[];
}

@InputType()
export class UpdateEpikBoxInput
  implements Partial<Pick<EpikBoxDocument, 'serialNumber' | 'vpnAddress' | 'status' | 'lastSeen'>>
{
  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  serialNumber?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  vpnAddress?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  status?: string;

  @Field(() => Date, { nullable: true })
  @IsOptional()
  lastSeen?: Date;
}

export class EpikBoxModel extends BaseModel<EpikBoxDocument> {
  constructor(fastify: FastifyInstance) {
    super(fastify, 'epikboxes');
  }
}
