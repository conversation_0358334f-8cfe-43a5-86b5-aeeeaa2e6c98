import { Field, ObjectType } from "type-graphql";
import { NumberSettings } from "./NumberSettings";

@ObjectType()
export class CASSettings {
  @Field(() => String, { nullable: true })
  channels?: string;

  @Field(() => String, { nullable: true })
  framing?: string;

  @Field(() => String, { nullable: true })
  lineCode?: string;

  @Field(() => String, { nullable: true })
  timeAndSource?: string;

  @Field(() => String, { nullable: true })
  bChannel?: string;

  @Field(() => String, { nullable: true })
  switchType?: string;

  @Field(() => Boolean, { nullable: true })
  echoCancellation?: boolean;

  @Field(() => String, { nullable: true })
  echoCancellationType?: string;

  @Field(() => String, { nullable: true })
  channelRange?: string;

  @Field(() => String, { nullable: true })
  authType?: string;

  @Field(() => String, { nullable: true })
  ip?: string;

  @Field(() => String, { nullable: true })
  username?: string;

  @Field(() => String, { nullable: true })
  password?: string;

  @Field(() => NumberSettings, { nullable: true })
  numberSettings?: NumberSettings;
}