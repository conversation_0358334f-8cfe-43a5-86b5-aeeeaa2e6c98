
import { BaseDocument } from '@/types/mongodb';
import { ObjectType, Field, ID } from 'type-graphql';
import { ObjectId } from 'mongodb';
import { BaseModel } from './base.model';
import { FastifyInstance } from 'fastify';
import { PortOutput } from '@/types/PortTypes';

@ObjectType()
export class ObiDocument implements BaseDocument {
  @Field(() => ID)
  _id?: ObjectId;

  @Field(() => String)
  deviceId: string;

  @Field(() => String, { nullable: true }) 
  obiNumber: string

  @Field(() => String, { nullable: true }) 
  macAddress: string

  @Field(() => PortOutput)
  port1: PortOutput;

  @Field(() => PortOutput)
  port2: PortOutput;
}

export class ObiModel extends BaseModel<ObiDocument> {
    constructor(fastify: FastifyInstance) {
      super(fastify, 'obis');
    }
}
