import { BaseDocument } from '@/types/mongodb';
import { ObjectType, Field, ID } from 'type-graphql';
import { ObjectId } from 'mongodb';
import { BaseModel } from './base.model';
import { FastifyInstance } from 'fastify';
import { IsOptional } from 'class-validator';

@ObjectType()
export class NumberDocument implements BaseDocument {
  @Field(() => ID)
  _id?: ObjectId;

  @Field(() => String)
  portLabel: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  callerIdName: string;
}

export class NumberModel extends BaseModel<NumberDocument> {
    constructor(fastify: FastifyInstance) {
      super(fastify, 'numbers');
    }
}