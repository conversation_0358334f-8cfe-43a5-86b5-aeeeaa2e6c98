import { BaseDocument } from '@/types/mongodb';
import { ObjectType, Field, ID } from 'type-graphql';
import { ObjectId } from 'mongodb';
import { BaseModel } from './base.model';
import { FastifyInstance } from 'fastify';

@ObjectType()
export class LocationDocument implements BaseDocument {
  @Field(() => ID)
  _id?: ObjectId;

  @Field(() => String)
  locationName: string;
}

export class LocationModel extends BaseModel<LocationDocument> {
    constructor(fastify: FastifyInstance) {
      super(fastify, 'locations');
    }
}