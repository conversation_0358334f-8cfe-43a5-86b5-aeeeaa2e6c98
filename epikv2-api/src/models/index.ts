import { config } from '@/config/environment';
import { createModuleLogger, dbLogger } from '@/utils/logger';
import mongodb from '@fastify/mongodb';
import { FastifyInstance } from 'fastify';
import fp from 'fastify-plugin';
import { EpikBoxModel, EpikBoxDocument } from './epikbox';
import { BaseModel } from './base.model';
import { ObiDocument, ObiModel } from './obi';
import { CompanyDocument, CompanyModel } from './company';

const logger = createModuleLogger('models');

export enum ModelName {
  EPIK_BOX = 'epikboxes',
  Obi = 'obis',
  Company = 'companiesv2'
}

// Type mapping for document types
type DocumentTypeMap = {
  [ModelName.EPIK_BOX]: EpikBoxDocument;
  [ModelName.Obi]: ObiDocument;
  [ModelName.Company]: CompanyDocument;
};

export type ModelInstanceMap = {
  [K in ModelName]: BaseModel<DocumentTypeMap[K]>
};

// const modelTypeMap: {
//   [K in ModelName]: new (fastify: FastifyInstance) => BaseModel<DocumentTypeMap[K]>
// } = {
//   [ModelName.EPIK_BOX]: EpikBoxModel,
//   [ModelName.Obis]: ObiModel,
// };


const modelRegistry: Partial<ModelInstanceMap> = {};

let isInitialized = false;

export function getModel<T extends ModelName>(name: T): ModelInstanceMap[T] {
  if (!isInitialized) {
    const error = new Error('Models not initialized. Call initializeModels first.');
    logger.error(error);
    throw error;
  }

  const model = modelRegistry[name] as ModelInstanceMap[T];
  if (!model) {
    const error = new Error(`Model ${name} not found in registry`);
    logger.error(error);
    throw error;
  }

  return model;
}

export function getAllModels(): ModelInstanceMap {
  if (!isInitialized) {
    const error = new Error('Models not initialized. Call initializeModels first.');
    logger.error(error);
    throw error;
  }

  return modelRegistry as ModelInstanceMap;
}

// function initializeModels(fastify: FastifyInstance): void {
//   if (isInitialized) {
//     logger.warn('Models already initialized, skipping initialization');
//     return;
//   }

//   if (!fastify.mongo || !fastify.mongo.db) {
//     const error = new Error(
//       'MongoDB not available. Register MongoDB plugin before initializing models'
//     );
//     logger.error(error);
//     throw error;
//   }

//   try {
//     const modelNames = Object.values(ModelName);

//     modelNames.forEach(name => {
//       // Get the constructor and create a new instance
//       const ModelConstructor = modelTypeMap[name];
//       modelRegistry[name] = new ModelConstructor(fastify);
//       logger.debug(`Initialized model: ${name}`);
//     });

//     isInitialized = true;
//     logger.info('All models initialized successfully');
//   } catch (error) {
//     logger.error({ error }, 'Failed to initialize models');
//     throw new Error(`Model initialization failed: ${(error as Error).message}`);
//   }
// }

function initializeModels(fastify: FastifyInstance): void {
  if (isInitialized) {
    logger.warn('Models already initialized, skipping initialization');
    return;
  }

  if (!fastify.mongo || !fastify.mongo.db) {
    const error = new Error(
      'MongoDB not available. Register MongoDB plugin before initializing models'
    );
    logger.error(error);
    throw error;
  }

  try {
    // Explicit initialization to avoid union type inference issues
    modelRegistry[ModelName.EPIK_BOX] = new EpikBoxModel(fastify);
    modelRegistry[ModelName.Obi] = new ObiModel(fastify);
    modelRegistry[ModelName.Company] = new CompanyModel(fastify);

    isInitialized = true;
    logger.info('All models initialized successfully');
  } catch (error) {
    logger.error({ error }, 'Failed to initialize models');
    throw new Error(`Model initialization failed: ${(error as Error).message}`);
  }
}


export const mongodbPlugin = fp(async function (fastify: FastifyInstance) {
  //extract db name from uri e.g mongodb://localhost:27017/epikFax?replicaSet=rs0
  const databaseName = config.mongoUri.match(/^mongodb(?:\+srv)?:\/\/[^/]+\/([^?]+)/)?.[1] || 'epikFax';
  dbLogger.info({ databaseName,uri: config.mongoUri }, 'Connecting to MongoDB');
  await fastify.register(mongodb, {
    forceClose: true,
    url: config.mongoUri,
    database: databaseName,
    maxPoolSize: 100,
    connectTimeoutMS: 5000,
    // logLevel: 'debug',
  });

  initializeModels(fastify);

  fastify.log.info('MongoDB connected and models initialized');
});

export { BaseModel } from './base.model';
export * from './epikbox';
export * from './company';
export * from './location';
export * from './obi';
export * from './number';
