import { Field, ObjectType } from "type-graphql";
import { NumberSettings } from "./NumberSettings";

@ObjectType()
export class PRISettings {
  @Field(() => String, { nullable: true })
  echoChannels?: string;

  @Field(() => String, { nullable: true })
  channels?: string;

  @Field(() => String, { nullable: true })
  framing?: string;

  @Field(() => String, { nullable: true })
  lineCode?: string;

  @Field(() => String, { nullable: true })
  timeAndSource?: string;

  @Field(() => String, { nullable: true })
  dChannel?: string;

  @Field(() => String, { nullable: true })
  bChannels?: string;

  @Field(() => String, { nullable: true })
  switchType?: string;

  @Field(() => Boolean, { nullable: true })
  echoCancellation?: boolean;

  @Field(() => String, { nullable: true })
  echoCancellationType?: string;

  @Field(() => NumberSettings, { nullable: true })
  numberSettings?: NumberSettings;
}