import { NotFoundError } from '@/middleware/errorHandler';
import { EpikBoxDocument, EpikBoxModel } from '@/models/epikbox';
import { JSONResponse } from '@/types/response';
import { respondJSON } from '@/utils/response';
import { FastifyInstance, FastifyRequest } from 'fastify';

interface EpikBoxParams {
  id: string;
}

interface CreateEpikBoxBody {
  serialNumber: string;
  vpnAddress: string;
  status?: string;
}

export async function epikboxRoutes(fastify: FastifyInstance) {
  const epikBoxModel = new EpikBoxModel(fastify);

  fastify.put(
    '/:id',
    async (
      request: FastifyRequest<{ Params: EpikBoxParams; Body: Partial<EpikBoxDocument> }>
    ): Promise<JSONResponse<EpikBoxDocument>> => {
      const epikbox = await epikBoxModel.update(request.params.id, request.body);
      if (!epikbox) {
        throw new NotFoundError('EpikBox');
      }
      return respondJSON(epikbox);
    }
  );
}
