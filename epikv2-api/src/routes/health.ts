import { FastifyInstance } from 'fastify';
import { respondJSON } from '@/utils/response';
import { Features } from '@/types';
import { ForbiddenError } from '@/middleware/errorHandler';

export async function healthRoutes(fastify: FastifyInstance): Promise<void> {
  fastify.get(
    '/',
    {
      preHandler: [fastify.requirePermissionHook(Features.EDGEDEVICES_LIST, 1)],
      schema: {
        description: 'Basic health check endpoint',
        tags: ['Health'],
        response: {
          200: {
            type: 'object',
            properties: {
              status: { type: 'string' },
              data: {
                type: 'object',
                properties: {
                  timestamp: { type: 'string' },
                  uptime: { type: 'number' },
                },
              },
            },
          },
        },
      },
    },
    async (_, reply) => {
      const healthData = {
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
      };

      return respondJSON(healthData);
    }
  );
}
