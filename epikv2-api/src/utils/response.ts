import { PaginatedResult, PaginationResponseOptions } from '@/types';
import type { JSONResponse } from '@/types/response';

export function respondJSON<T>(data: T): JSONResponse<T> {
  return {
    status: 'success',
    data,
  } as JSONResponse<T>;
}

export function respondPaginated<T>(
  docs: T[],
  pagination: PaginationResponseOptions
): JSONResponse<PaginatedResult<T>> {
  return respondJSON({
    pagination,
    docs,
  });
}
