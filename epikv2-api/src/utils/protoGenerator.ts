import { createModuleLogger } from '@/utils/logger';
import * as fs from 'fs';
import * as path from 'path';
import { Client } from 'grpc-reflection-js';
import * as grpc from '@grpc/grpc-js';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);
const logger = createModuleLogger('proto-generator');

export interface ProtoGenerationConfig {
  serverAddress: string;
  serviceName: string;
  packageName: string;
  outputDir?: string;
  protoDir?: string;
  generateTypes?: boolean;
  forceRegenerate?: boolean;
  // Kubernetes-specific options
  useKubectl?: boolean;
  kubeNamespace?: string;
  kubeServiceName?: string;
  kubeServicePort?: number;
  localPort?: number;
  kubeContext?: string;
}

export interface ProtoGenerationResult {
  protoFilePath: string;
  typesFilePath?: string;
  success: boolean;
  error?: string;
  portForwardPid?: number; // Process ID of kubectl port-forward if used
}

export class ProtoGenerator {
  private config: ProtoGenerationConfig;
  private protoDir: string;
  private outputDir: string;
  private portForwardProcess: any = null;

  constructor(config: ProtoGenerationConfig) {
    this.config = config;
    this.protoDir = config.protoDir || path.join(process.cwd(), 'proto');
    this.outputDir = config.outputDir || path.join(process.cwd(), 'src', 'types', 'generated');

    // Ensure directories exist
    this.ensureDirectories();
  }

  private ensureDirectories(): void {
    if (!fs.existsSync(this.protoDir)) {
      fs.mkdirSync(this.protoDir, { recursive: true });
      logger.info(`Created proto directory: ${this.protoDir}`);
    }
    
    if (!fs.existsSync(this.outputDir)) {
      fs.mkdirSync(this.outputDir, { recursive: true });
      logger.info(`Created output directory: ${this.outputDir}`);
    }
  }

  private getProtoFileName(): string {
    return `${this.config.serviceName.toLowerCase()}.proto`;
  }

  private getProtoFilePath(): string {
    return path.join(this.protoDir, this.getProtoFileName());
  }

  private getTypesFilePath(): string {
    return path.join(this.outputDir, `${this.config.serviceName.toLowerCase()}_pb.ts`);
  }

  private shouldRegenerate(): boolean {
    if (this.config.forceRegenerate) {
      return true;
    }

    const protoPath = this.getProtoFilePath();
    const typesPath = this.getTypesFilePath();

    // If proto file doesn't exist, we need to generate
    if (!fs.existsSync(protoPath)) {
      return true;
    }

    // If types are requested but don't exist, we need to generate
    if (this.config.generateTypes !== false && !fs.existsSync(typesPath)) {
      return true;
    }

    return false;
  }

  private async setupKubectlPortForward(): Promise<{ serverAddress: string; cleanup: () => Promise<void> }> {
    if (!this.config.useKubectl) {
      return {
        serverAddress: this.config.serverAddress,
        cleanup: async () => {}
      };
    }

    const namespace = this.config.kubeNamespace || 'default';
    const serviceName = this.config.kubeServiceName || this.config.serviceName.toLowerCase();
    const servicePort = this.config.kubeServicePort || 50051;
    const localPort = this.config.localPort || 50051;
    const context = this.config.kubeContext ? `--context=${this.config.kubeContext}` : '';

    logger.info(`Setting up kubectl port-forward for service ${serviceName} in namespace ${namespace}`);

    // Build kubectl command
    const kubectlCmd = [
      'kubectl',
      'port-forward',
      context,
      `-n ${namespace}`,
      `service/${serviceName}`,
      `${localPort}:${servicePort}`
    ].filter(Boolean).join(' ');

    logger.debug(`Running: ${kubectlCmd}`);

    return new Promise((resolve, reject) => {
      const { spawn } = require('child_process');

      this.portForwardProcess = spawn('kubectl', [
        'port-forward',
        ...(this.config.kubeContext ? [`--context=${this.config.kubeContext}`] : []),
        `-n`, namespace,
        `service/${serviceName}`,
        `${localPort}:${servicePort}`
      ], {
        stdio: ['ignore', 'pipe', 'pipe']
      });

      let isReady = false;
      const timeout = setTimeout(() => {
        if (!isReady) {
          this.portForwardProcess?.kill();
          reject(new Error('Timeout waiting for kubectl port-forward to be ready'));
        }
      }, 10000); // 10 second timeout

      this.portForwardProcess.stdout?.on('data', (data: Buffer) => {
        const output = data.toString();
        logger.debug(`kubectl stdout: ${output}`);

        if (output.includes('Forwarding from') && !isReady) {
          isReady = true;
          clearTimeout(timeout);
          logger.info(`Port forwarding established: localhost:${localPort} -> ${serviceName}:${servicePort}`);

          resolve({
            serverAddress: `localhost:${localPort}`,
            cleanup: async () => {
              if (this.portForwardProcess) {
                logger.info('Cleaning up kubectl port-forward process');
                this.portForwardProcess.kill();
                this.portForwardProcess = null;
              }
            }
          });
        }
      });

      this.portForwardProcess.stderr?.on('data', (data: Buffer) => {
        const error = data.toString();
        logger.warn(`kubectl stderr: ${error}`);

        if (error.includes('error') || error.includes('Error')) {
          clearTimeout(timeout);
          reject(new Error(`kubectl port-forward failed: ${error}`));
        }
      });

      this.portForwardProcess.on('exit', (code: number) => {
        clearTimeout(timeout);
        if (!isReady) {
          reject(new Error(`kubectl port-forward exited with code ${code}`));
        }
      });
    });
  }

  async downloadProtoFile(): Promise<string> {
    const protoPath = this.getProtoFilePath();

    if (!this.shouldRegenerate()) {
      logger.info(`Proto file already exists and regeneration not forced: ${protoPath}`);
      return protoPath;
    }

    logger.info(`Downloading proto file for service ${this.config.serviceName}`);

    // Setup kubectl port forwarding if needed
    const { serverAddress, cleanup } = await this.setupKubectlPortForward();

    try {
      logger.info(`Connecting to gRPC server at ${serverAddress}`);
      const reflectionClient = new Client(serverAddress, grpc.credentials.createInsecure());

      // List available services
      const services = await reflectionClient.listServices();
      logger.debug('Available services:', services);

      // Find the target service
      const targetServiceName = services.find((service: string | void) =>
        service?.toLowerCase().includes(this.config.serviceName.toLowerCase())
      );

      if (!targetServiceName) {
        throw new Error(`Service ${this.config.serviceName} not found on server. Available services: ${services.join(', ')}`);
      }

      logger.info(`Found target service: ${targetServiceName}`);

      // Get the proto file descriptor
      const root = await reflectionClient.fileContainingSymbol(targetServiceName);
      const protoJson = root.toJSON();

      // Convert JSON back to proto format
      const protoContent = this.jsonToProto(protoJson);

      // Write proto file
      fs.writeFileSync(protoPath, protoContent);
      logger.info(`Proto file saved to: ${protoPath}`);

      return protoPath;
    } catch (error) {
      logger.error(`Failed to download proto file: ${error}`);
      throw error;
    } finally {
      // Clean up port forwarding
      await cleanup();
    }
  }

  private jsonToProto(protoJson: any): string {
    // This is a simplified conversion - you might need to enhance this based on your proto structure
    let protoContent = '';
    
    // Add syntax declaration
    protoContent += 'syntax = "proto3";\n\n';
    
    // Add package declaration
    if (this.config.packageName) {
      protoContent += `package ${this.config.packageName};\n\n`;
    }

    // Add imports if any
    if (protoJson.imports && protoJson.imports.length > 0) {
      protoJson.imports.forEach((imp: string) => {
        protoContent += `import "${imp}";\n`;
      });
      protoContent += '\n';
    }

    // Add nested types and services
    if (protoJson.nested) {
      protoContent += this.processNested(protoJson.nested, 0);
    }

    return protoContent;
  }

  private processNested(nested: any, indent: number): string {
    let content = '';
    const indentStr = '  '.repeat(indent);

    for (const [name, definition] of Object.entries(nested)) {
      const def = definition as any;
      
      if (def.methods) {
        // This is a service
        content += `${indentStr}service ${name} {\n`;
        for (const [methodName, method] of Object.entries(def.methods)) {
          const methodDef = method as any;
          content += `${indentStr}  rpc ${methodName}(${methodDef.requestType}) returns (${methodDef.responseType});\n`;
        }
        content += `${indentStr}}\n\n`;
      } else if (def.fields) {
        // This is a message
        content += `${indentStr}message ${name} {\n`;
        for (const [fieldName, field] of Object.entries(def.fields)) {
          const fieldDef = field as any;
          const fieldType = fieldDef.type || 'string';
          const fieldNumber = fieldDef.id || 1;
          content += `${indentStr}  ${fieldType} ${fieldName} = ${fieldNumber};\n`;
        }
        content += `${indentStr}}\n\n`;
      } else if (def.nested) {
        // This is a nested namespace
        content += `${indentStr}message ${name} {\n`;
        content += this.processNested(def.nested, indent + 1);
        content += `${indentStr}}\n\n`;
      }
    }

    return content;
  }

  async generateTypeScriptTypes(): Promise<string> {
    const protoPath = this.getProtoFilePath();
    const typesPath = this.getTypesFilePath();

    if (!fs.existsSync(protoPath)) {
      throw new Error(`Proto file not found: ${protoPath}. Run downloadProtoFile() first.`);
    }

    if (!this.shouldRegenerate()) {
      logger.info(`TypeScript types already exist and regeneration not forced: ${typesPath}`);
      return typesPath;
    }

    logger.info(`Generating TypeScript types from ${protoPath}`);

    try {
      // Find protoc-gen-ts plugin path
      const pluginPath = await this.findProtocGenTsPlugin();
      
      // Run protoc command
      const command = `protoc --plugin=protoc-gen-ts=${pluginPath} --ts_out=${this.outputDir} --proto_path=${this.protoDir} ${this.getProtoFileName()}`;
      
      logger.debug(`Running command: ${command}`);
      
      const { stdout, stderr } = await execAsync(command);
      
      if (stderr) {
        logger.warn(`protoc stderr: ${stderr}`);
      }
      
      if (stdout) {
        logger.debug(`protoc stdout: ${stdout}`);
      }

      if (fs.existsSync(typesPath)) {
        logger.info(`TypeScript types generated successfully: ${typesPath}`);
        return typesPath;
      } else {
        throw new Error('TypeScript types file was not generated');
      }
    } catch (error) {
      logger.error(`Failed to generate TypeScript types: ${error}`);
      throw error;
    }
  }

  private async findProtocGenTsPlugin(): Promise<string> {
    // Try to find protoc-gen-ts in node_modules
    const possiblePaths = [
      path.join(process.cwd(), 'node_modules', '.bin', 'protoc-gen-ts'),
      path.join(process.cwd(), 'node_modules', 'protoc-gen-ts', 'bin', 'protoc-gen-ts'),
      'protoc-gen-ts' // Assume it's in PATH
    ];

    for (const pluginPath of possiblePaths) {
      try {
        await execAsync(`which ${pluginPath}`);
        return pluginPath;
      } catch {
        // Continue to next path
      }
    }

    throw new Error('protoc-gen-ts plugin not found. Please install it with: npm install --save-dev protoc-gen-ts');
  }

  async generateAll(): Promise<ProtoGenerationResult> {
    try {
      const protoFilePath = await this.downloadProtoFile();

      let typesFilePath: string | undefined;
      if (this.config.generateTypes !== false) {
        typesFilePath = await this.generateTypeScriptTypes();
      }

      return {
        protoFilePath,
        typesFilePath,
        success: true,
        portForwardPid: this.portForwardProcess?.pid
      };
    } catch (error) {
      logger.error(`Proto generation failed: ${error}`);
      return {
        protoFilePath: this.getProtoFilePath(),
        typesFilePath: this.getTypesFilePath(),
        success: false,
        error: error instanceof Error ? error.message : String(error),
        portForwardPid: this.portForwardProcess?.pid
      };
    }
  }
}

// Helper function to generate proto and types for a service
export async function generateProtoAndTypes(config: ProtoGenerationConfig): Promise<ProtoGenerationResult> {
  const generator = new ProtoGenerator(config);
  return generator.generateAll();
}

// Helper function to check if kubectl is available
export async function checkKubectlAvailable(): Promise<boolean> {
  try {
    await execAsync('kubectl version --client');
    return true;
  } catch {
    return false;
  }
}

// Helper function to check if a Kubernetes service exists
export async function checkKubernetesService(
  serviceName: string,
  namespace = 'default',
  context?: string
): Promise<boolean> {
  try {
    const contextFlag = context ? `--context=${context}` : '';
    const command = `kubectl get service ${serviceName} -n ${namespace} ${contextFlag}`.trim();
    await execAsync(command);
    return true;
  } catch {
    return false;
  }
}

// Helper function to get available Kubernetes services
export async function listKubernetesServices(
  namespace = 'default',
  context?: string
): Promise<string[]> {
  try {
    const contextFlag = context ? `--context=${context}` : '';
    const command = `kubectl get services -n ${namespace} ${contextFlag} -o jsonpath='{.items[*].metadata.name}'`.trim();
    const { stdout } = await execAsync(command);
    return stdout.trim().split(/\s+/).filter(Boolean);
  } catch {
    return [];
  }
}

// Helper function to check if proto files exist and are up to date
export function checkProtoFiles(serviceName: string, protoDir?: string, outputDir?: string): {
  protoExists: boolean;
  typesExist: boolean;
  protoPath: string;
  typesPath: string;
} {
  const protoPath = path.join(protoDir || path.join(process.cwd(), 'proto'), `${serviceName.toLowerCase()}.proto`);
  const typesPath = path.join(outputDir || path.join(process.cwd(), 'src', 'types', 'generated'), `${serviceName.toLowerCase()}_pb.ts`);

  return {
    protoExists: fs.existsSync(protoPath),
    typesExist: fs.existsSync(typesPath),
    protoPath,
    typesPath
  };
}
