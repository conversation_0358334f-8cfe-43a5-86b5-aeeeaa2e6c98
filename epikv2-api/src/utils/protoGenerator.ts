import { createModuleLogger } from '@/utils/logger';
import * as fs from 'fs';
import * as path from 'path';
import { Client } from 'grpc-reflection-js';
import * as grpc from '@grpc/grpc-js';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);
const logger = createModuleLogger('proto-generator');

export interface ProtoGenerationConfig {
  serverAddress: string;
  serviceName: string;
  packageName: string;
  outputDir?: string;
  protoDir?: string;
  generateTypes?: boolean;
  forceRegenerate?: boolean;
}

export interface ProtoGenerationResult {
  protoFilePath: string;
  typesFilePath?: string;
  success: boolean;
  error?: string;
}

export class ProtoGenerator {
  private config: ProtoGenerationConfig;
  private protoDir: string;
  private outputDir: string;

  constructor(config: ProtoGenerationConfig) {
    this.config = config;
    this.protoDir = config.protoDir || path.join(process.cwd(), 'proto');
    this.outputDir = config.outputDir || path.join(process.cwd(), 'src', 'types', 'generated');
    
    // Ensure directories exist
    this.ensureDirectories();
  }

  private ensureDirectories(): void {
    if (!fs.existsSync(this.protoDir)) {
      fs.mkdirSync(this.protoDir, { recursive: true });
      logger.info(`Created proto directory: ${this.protoDir}`);
    }
    
    if (!fs.existsSync(this.outputDir)) {
      fs.mkdirSync(this.outputDir, { recursive: true });
      logger.info(`Created output directory: ${this.outputDir}`);
    }
  }

  private getProtoFileName(): string {
    return `${this.config.serviceName.toLowerCase()}.proto`;
  }

  private getProtoFilePath(): string {
    return path.join(this.protoDir, this.getProtoFileName());
  }

  private getTypesFilePath(): string {
    return path.join(this.outputDir, `${this.config.serviceName.toLowerCase()}_pb.ts`);
  }

  private shouldRegenerate(): boolean {
    if (this.config.forceRegenerate) {
      return true;
    }

    const protoPath = this.getProtoFilePath();
    const typesPath = this.getTypesFilePath();

    // If proto file doesn't exist, we need to generate
    if (!fs.existsSync(protoPath)) {
      return true;
    }

    // If types are requested but don't exist, we need to generate
    if (this.config.generateTypes !== false && !fs.existsSync(typesPath)) {
      return true;
    }

    return false;
  }

  async downloadProtoFile(): Promise<string> {
    const protoPath = this.getProtoFilePath();
    
    if (!this.shouldRegenerate()) {
      logger.info(`Proto file already exists and regeneration not forced: ${protoPath}`);
      return protoPath;
    }

    logger.info(`Downloading proto file for service ${this.config.serviceName} from ${this.config.serverAddress}`);

    const reflectionClient = new Client(this.config.serverAddress, grpc.credentials.createInsecure());

    try {
      // List available services
      const services = await reflectionClient.listServices();
      logger.debug('Available services:', services);

      // Find the target service
      const targetServiceName = services.find((service: string | void) =>
        service?.toLowerCase().includes(this.config.serviceName.toLowerCase())
      );

      if (!targetServiceName) {
        throw new Error(`Service ${this.config.serviceName} not found on server. Available services: ${services.join(', ')}`);
      }

      logger.info(`Found target service: ${targetServiceName}`);

      // Get the proto file descriptor
      const root = await reflectionClient.fileContainingSymbol(targetServiceName);
      const protoJson = root.toJSON();

      // Convert JSON back to proto format
      const protoContent = this.jsonToProto(protoJson);

      // Write proto file
      fs.writeFileSync(protoPath, protoContent);
      logger.info(`Proto file saved to: ${protoPath}`);

      return protoPath;
    } catch (error) {
      logger.error(`Failed to download proto file: ${error}`);
      throw error;
    }
  }

  private jsonToProto(protoJson: any): string {
    // This is a simplified conversion - you might need to enhance this based on your proto structure
    let protoContent = '';
    
    // Add syntax declaration
    protoContent += 'syntax = "proto3";\n\n';
    
    // Add package declaration
    if (this.config.packageName) {
      protoContent += `package ${this.config.packageName};\n\n`;
    }

    // Add imports if any
    if (protoJson.imports && protoJson.imports.length > 0) {
      protoJson.imports.forEach((imp: string) => {
        protoContent += `import "${imp}";\n`;
      });
      protoContent += '\n';
    }

    // Add nested types and services
    if (protoJson.nested) {
      protoContent += this.processNested(protoJson.nested, 0);
    }

    return protoContent;
  }

  private processNested(nested: any, indent: number): string {
    let content = '';
    const indentStr = '  '.repeat(indent);

    for (const [name, definition] of Object.entries(nested)) {
      const def = definition as any;
      
      if (def.methods) {
        // This is a service
        content += `${indentStr}service ${name} {\n`;
        for (const [methodName, method] of Object.entries(def.methods)) {
          const methodDef = method as any;
          content += `${indentStr}  rpc ${methodName}(${methodDef.requestType}) returns (${methodDef.responseType});\n`;
        }
        content += `${indentStr}}\n\n`;
      } else if (def.fields) {
        // This is a message
        content += `${indentStr}message ${name} {\n`;
        for (const [fieldName, field] of Object.entries(def.fields)) {
          const fieldDef = field as any;
          const fieldType = fieldDef.type || 'string';
          const fieldNumber = fieldDef.id || 1;
          content += `${indentStr}  ${fieldType} ${fieldName} = ${fieldNumber};\n`;
        }
        content += `${indentStr}}\n\n`;
      } else if (def.nested) {
        // This is a nested namespace
        content += `${indentStr}message ${name} {\n`;
        content += this.processNested(def.nested, indent + 1);
        content += `${indentStr}}\n\n`;
      }
    }

    return content;
  }

  async generateTypeScriptTypes(): Promise<string> {
    const protoPath = this.getProtoFilePath();
    const typesPath = this.getTypesFilePath();

    if (!fs.existsSync(protoPath)) {
      throw new Error(`Proto file not found: ${protoPath}. Run downloadProtoFile() first.`);
    }

    if (!this.shouldRegenerate()) {
      logger.info(`TypeScript types already exist and regeneration not forced: ${typesPath}`);
      return typesPath;
    }

    logger.info(`Generating TypeScript types from ${protoPath}`);

    try {
      // Find protoc-gen-ts plugin path
      const pluginPath = await this.findProtocGenTsPlugin();
      
      // Run protoc command
      const command = `protoc --plugin=protoc-gen-ts=${pluginPath} --ts_out=${this.outputDir} --proto_path=${this.protoDir} ${this.getProtoFileName()}`;
      
      logger.debug(`Running command: ${command}`);
      
      const { stdout, stderr } = await execAsync(command);
      
      if (stderr) {
        logger.warn(`protoc stderr: ${stderr}`);
      }
      
      if (stdout) {
        logger.debug(`protoc stdout: ${stdout}`);
      }

      if (fs.existsSync(typesPath)) {
        logger.info(`TypeScript types generated successfully: ${typesPath}`);
        return typesPath;
      } else {
        throw new Error('TypeScript types file was not generated');
      }
    } catch (error) {
      logger.error(`Failed to generate TypeScript types: ${error}`);
      throw error;
    }
  }

  private async findProtocGenTsPlugin(): Promise<string> {
    // Try to find protoc-gen-ts in node_modules
    const possiblePaths = [
      path.join(process.cwd(), 'node_modules', '.bin', 'protoc-gen-ts'),
      path.join(process.cwd(), 'node_modules', 'protoc-gen-ts', 'bin', 'protoc-gen-ts'),
      'protoc-gen-ts' // Assume it's in PATH
    ];

    for (const pluginPath of possiblePaths) {
      try {
        await execAsync(`which ${pluginPath}`);
        return pluginPath;
      } catch {
        // Continue to next path
      }
    }

    throw new Error('protoc-gen-ts plugin not found. Please install it with: npm install --save-dev protoc-gen-ts');
  }

  async generateAll(): Promise<ProtoGenerationResult> {
    try {
      const protoFilePath = await this.downloadProtoFile();
      
      let typesFilePath: string | undefined;
      if (this.config.generateTypes !== false) {
        typesFilePath = await this.generateTypeScriptTypes();
      }

      return {
        protoFilePath,
        typesFilePath,
        success: true
      };
    } catch (error) {
      logger.error(`Proto generation failed: ${error}`);
      return {
        protoFilePath: this.getProtoFilePath(),
        typesFilePath: this.getTypesFilePath(),
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }
}

// Helper function to generate proto and types for a service
export async function generateProtoAndTypes(config: ProtoGenerationConfig): Promise<ProtoGenerationResult> {
  const generator = new ProtoGenerator(config);
  return generator.generateAll();
}

// Helper function to check if proto files exist and are up to date
export function checkProtoFiles(serviceName: string, protoDir?: string, outputDir?: string): {
  protoExists: boolean;
  typesExist: boolean;
  protoPath: string;
  typesPath: string;
} {
  const protoPath = path.join(protoDir || path.join(process.cwd(), 'proto'), `${serviceName.toLowerCase()}.proto`);
  const typesPath = path.join(outputDir || path.join(process.cwd(), 'src', 'types', 'generated'), `${serviceName.toLowerCase()}_pb.ts`);

  return {
    protoExists: fs.existsSync(protoPath),
    typesExist: fs.existsSync(typesPath),
    protoPath,
    typesPath
  };
}
