# gRPC Service Helper

This helper provides a reusable way to connect to gRPC services without code duplication.

## Files

- `grpcClient.ts` - Generic gRPC client helper class
- `../examples/exampleGrpcService.ts` - Example implementation
- `../middleware/permissionClient.ts` - Real-world usage example

## Quick Start

### 1. Create a Service Client

```typescript
import { GrpcServiceClient } from '@/utils/grpcClient';

export class YourServiceClient extends GrpcServiceClient {
  constructor(serverAddress?: string) {
    super({
      serverAddress: serverAddress || 'your-grpc-server:50051',
      serviceName: 'YourServiceName', // Must match the service name in your .proto file
      packageName: 'your.package.name', // Must match the package in your .proto file
      maxRetries: 3,
      retryDelay: 1000
    });
  }

  // Add your service methods
  async yourMethod(params: any): Promise<any> {
    return this.makeRequest<any>(
      'YourGrpcMethod', // gRPC method name
      params, // request parameters
      'response_field' // optional: extract specific field from response
    );
  }
}
```

### 2. Create a Fastify Plugin

```typescript
import { safeGrpcOperation } from '@/utils/grpcClient';
import { createModuleLogger } from '@/utils/logger';
import fp from 'fastify-plugin';

const yourServiceClient = new YourServiceClient();
const logger = createModuleLogger('yourService');

export const yourServicePlugin = fp(async function (fastify: FastifyInstance) {
  try {
    await yourServiceClient.connect();
  } catch (error) {
    fastify.log.warn('Your service not available, will retry on first request');
  }

  fastify.decorate(
    'yourMethod',
    async (request: FastifyRequest, params: any): Promise<any> => {
      return await safeGrpcOperation(async () => {
        return await yourServiceClient.yourMethod(params);
      }, 'your operation description', logger);
    }
  );
});
```

### 3. Register and Use

```typescript
// Register the plugin
await app.register(yourServicePlugin);

// Use in routes
app.get('/your-endpoint', async (request, reply) => {
  const result = await app.yourMethod(request, { param: 'value' });
  return result;
});
```

## Configuration Options

### GrpcServiceConfig

- `serverAddress`: gRPC server address (e.g., 'localhost:50051')
- `serviceName`: Service name from your .proto file
- `packageName`: Package name from your .proto file
- `maxRetries`: Maximum connection retry attempts (default: 3)
- `retryDelay`: Delay between retries in milliseconds (default: 1000)

## Methods

### GrpcServiceClient

- `connect(retry?: boolean)`: Connect to the gRPC service
- `makeRequest<T>(method, params, responseField?)`: Make a gRPC request
- `getClient()`: Get the raw gRPC client
- `isServiceConnected()`: Check connection status

### safeGrpcOperation

Wrapper for safe gRPC operations with error handling:

```typescript
safeGrpcOperation<T>(
  operation: () => Promise<T>,
  context: string,
  logger: any,
  defaultValue?: T
): Promise<T>
```

## Features

- **Automatic Connection Management**: Handles connection retries and reconnection
- **Error Handling**: Built-in error handling with logging
- **Type Safety**: Full TypeScript support
- **Reflection Support**: Uses gRPC reflection to discover services
- **Reusable**: One helper for all your gRPC services
- **Fastify Integration**: Easy integration with Fastify plugins

## Environment Variables

Make sure to set your gRPC service addresses in your environment configuration:

```typescript
// config/environment.ts
export const config = {
  authGrpcUri: process.env.AUTH_GRPC_URI || 'localhost:50051',
  yourServiceGrpcUri: process.env.YOUR_SERVICE_GRPC_URI || 'localhost:50052',
  // ... other config
};
```

## Error Handling

The helper includes automatic error handling:

- Connection retries with exponential backoff
- Graceful degradation when services are unavailable
- Detailed logging for debugging
- Safe operation wrappers that return default values on failure

## Best Practices

1. **Use environment variables** for service addresses
2. **Create dedicated client classes** for each service
3. **Use safeGrpcOperation** for all service calls
4. **Set appropriate retry limits** based on your use case
5. **Include meaningful context** in error messages
6. **Test service availability** during application startup
