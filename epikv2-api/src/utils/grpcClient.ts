import { createModuleLogger } from '@/utils/logger';
import * as grpc from '@grpc/grpc-js';
import * as protoLoader from '@grpc/proto-loader';
import * as fs from 'fs';
import * as path from 'path';

// Generic gRPC client configuration interface
export interface GrpcServiceConfig {
  serverAddress: string;
  serviceName: string;
  packageName: string;
  maxRetries?: number;
  retryDelay?: number;
  protoDir?: string; // Directory where proto files are stored
}

// Generic gRPC client helper class
export class GrpcServiceClient {
  private client: any = null;
  private serverAddress: string;
  private serviceName: string;
  private packageName: string;
  private isConnected = false;
  private connectionAttempts = 0;
  private readonly MAX_RETRIES: number;
  private readonly RETRY_DELAY: number;
  private readonly protoDir: string;
  private logger: any;

  constructor(config: GrpcServiceConfig) {
    this.serverAddress = config.serverAddress;
    this.serviceName = config.serviceName;
    this.packageName = config.packageName;
    this.MAX_RETRIES = config.maxRetries || 3;
    this.RETRY_DELAY = config.retryDelay || 1000;
    this.protoDir = config.protoDir || path.join(process.cwd(), 'proto');
    this.logger = createModuleLogger(`grpc-${this.serviceName.toLowerCase()}`);
  }

  async connect(retry = true): Promise<void> {
    if (this.isConnected) {
      return;
    }

    if (this.connectionAttempts >= this.MAX_RETRIES) {
      throw new Error(`Failed to connect to gRPC service after ${this.MAX_RETRIES} attempts`);
    }

    try {
      // Load proto files directly
      const packageDefinition = await this.loadFromProtoFiles();
      const protoDescriptor = grpc.loadPackageDefinition(packageDefinition);

      // Handle nested package names like 'edge.v1'
      let packageObj = protoDescriptor;
      const packageParts = this.packageName.split('.');

      for (const part of packageParts) {
        packageObj = (packageObj as any)?.[part];
        if (!packageObj) {
          this.logger.error(`Package part '${part}' not found in package '${this.packageName}'`);
          this.logger.error('Available packages:', Object.keys(protoDescriptor));
          throw new Error(`Package '${this.packageName}' not found`);
        }
      }

      const serviceConstructor = (packageObj as any)?.[this.serviceName];

      if (!serviceConstructor) {
        this.logger.error(`Available services in package '${this.packageName}':`, Object.keys(packageObj));
        throw new Error(`Service ${this.serviceName} not found in package ${this.packageName}`);
      }

      this.client = new serviceConstructor(this.serverAddress, grpc.credentials.createInsecure());
      this.isConnected = true;
      this.connectionAttempts = 0;
      this.logger.info(`Successfully connected to gRPC service at ${this.serverAddress}`);
    } catch (error) {
      this.connectionAttempts++;
      this.logger.error(`Failed to connect to gRPC service at ${this.serverAddress}: ${error}`);

      if (retry && this.connectionAttempts < this.MAX_RETRIES) {
        this.logger.info(
          `Retrying connection (attempt ${this.connectionAttempts + 1}/${this.MAX_RETRIES})...`
        );
        await new Promise(resolve => setTimeout(resolve, this.RETRY_DELAY));
        return this.connect();
      }

      throw error;
    }
  }

  private async loadFromProtoFiles(): Promise<any> {
    const protoFileName = `${this.serviceName.toLowerCase()}.proto`;
    const protoFilePath = path.join(this.protoDir, protoFileName);

    // Check if proto file exists
    if (!fs.existsSync(protoFilePath)) {
      throw new Error(`Proto file not found: ${protoFilePath}. Please generate it first using the generate-proto script.`);
    }

    this.logger.info(`Loading proto file: ${protoFilePath}`);

    const packageDefinition = protoLoader.loadSync(protoFilePath, {
      keepCase: true,
      longs: String,
      enums: String,
      defaults: true,
      oneofs: true
    });

    return packageDefinition;
  }

  async makeRequest<T>(
    method: string,
    params: Record<string, any>,
    responseField?: string
  ): Promise<T> {
    await this.connect();

    return new Promise((resolve, reject) => {
      this.client[method](params, (error: any, response: any) => {
        this.logger.debug({ method, params, response }, 'gRPC request');
        if (responseField && response[responseField]) {
          resolve(response[responseField]);
          return;
        } else if (responseField || error) {
          reject(error || 'Unable to validate');
          return false;
        }
        resolve(response);
      });
    });
  }

  getClient(): any {
    return this.client;
  }

  isServiceConnected(): boolean {
    return this.isConnected;
  }
}

// Helper function for safe gRPC operations with error handling
export async function safeGrpcOperation<T = boolean>(
  operation: () => Promise<T>,
  context: string,
  logger: any,
  defaultValue?: T
): Promise<T> {
  try {
    return await operation();
  } catch (_err) {
    logger.error({ _err, context }, `Failed to ${context}`);
    return (defaultValue !== undefined ? defaultValue : false) as T;
  }
}

// Factory function to create gRPC service clients
export function createGrpcServiceClient(config: GrpcServiceConfig): GrpcServiceClient {
  return new GrpcServiceClient(config);
}


