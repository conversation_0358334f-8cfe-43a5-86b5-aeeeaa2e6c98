import pino from 'pino';
import { config } from '@/config/environment';

export const logger = pino({
  level: config.devMode ? 'debug' : 'info',
  timestamp: pino.stdTimeFunctions.isoTime,
  formatters: {
    level: label => ({ level: label }),
  },
  ...(config.devMode && {
    transport: {
      target: 'pino-pretty',
      options: {
        colorize: true,
        translateTime: 'yyyy-mm-dd HH:MM:ss',
        ignore: 'pid,hostname',
        singleLine: true,
      },
    },
  }),
  ...(!config.devMode && {
    redact: {
      paths: [
        'req.headers.authorization',
        'req.headers.cookie',
        'res.headers["set-cookie"]',
        'password',
        'token',
        'secret',
      ],
      censor: '[REDACTED]',
    },
  }),
});

export const createModuleLogger = (module: string) => {
  return logger.child({ module });
};

export const apiLogger = createModuleLogger('api');
export const performanceLogger = createModuleLogger('performance');
export const dbLogger = createModuleLogger('database');


export const logRequestTiming = (startTime: number, operation: string) => {
  const duration = Date.now() - startTime;
  performanceLogger.info({ duration, operation }, `Operation completed in ${duration}ms`);

  if (duration > 1000) {
    performanceLogger.warn({ duration, operation }, `Slow operation detected: ${operation}`);
  }
};

export default logger;
