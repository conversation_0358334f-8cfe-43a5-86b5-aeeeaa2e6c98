import { config } from '@/config/environment';
import type { GraphqlContext } from '@/types/fastify';
import { FastifyInstance } from 'fastify';
import fp from 'fastify-plugin';
import mercurius from 'mercurius';
import 'reflect-metadata';
import { buildSchema, GraphQLTimestamp } from 'type-graphql';

// Import resolvers
import { EpikBoxResolver } from './resolvers/EpikBoxResolver';
import { PortOutputResolver } from './resolvers/PortOutputResolver';
import { customAuthChecker } from '@/middleware/graphql';
import { ObjectId } from 'mongodb';
import { ObjectIdScalar } from '@/types';

export const graphqlPlugin = fp(async function (fastify: FastifyInstance) {
  // Build the schema using type-graphql
  const schema = await buildSchema({
    resolvers: [EpikBoxResolver, PortOutputResolver],
    emitSchemaFile: config.devMode ? 'schema.graphql' : false,
    validate: true, // Enable validation with class-validator
    authChecker: customAuthChe<PERSON>,
    authMode: 'error',
    scalarsMap: [{ type: ObjectId, scalar: ObjectIdScalar },{ type: Date, scalar: GraphQLTimestamp }],
    container: {
      get: (resolverClass) => {
        return new resolverClass(fastify);
      },
    },
  });

  await fastify.register(mercurius, {
    schema,
    graphiql: config.devMode, // Enable GraphiQL UI for development
    path: '/query',
    context: (request, reply): GraphqlContext => {
      return {
        reply,
        request,
        fastify,
        user: request.user || null,
      };
    },
  });
});
