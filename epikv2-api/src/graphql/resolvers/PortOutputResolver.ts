import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ol<PERSON>, <PERSON> } from 'type-graphql';
import { PortOutput } from '@/types/PortTypes';
import type { FastifyInstance } from 'fastify';
import { NumberDocument, NumberModel } from '@/models';

@Resolver(() => PortOutput)
export class PortOutputResolver {
  private numberModel: NumberModel;

  constructor(fastify: FastifyInstance) {
    this.numberModel = new NumberModel(fastify);
  }

  @FieldResolver(() => NumberDocument, { nullable: true })
  async assignedNumberDoc(@Root() port: PortOutput): Promise<NumberDocument | null> {
    if (!port.assignedNumberRef) return null;
    return await this.numberModel.findById(port.assignedNumberRef.toString());
  }
}
