import { EpikBoxDocument, EpikBoxFilterInput } from '@/models/epikbox';
import {
  Features,
  ListEpikBoxPaginatedResult,
  PaginationInput,
  ValidateOperations,
} from '@/types/index';
import { Arg, Ctx, Query, Resolver, FieldResolver, Root, Authorized } from 'type-graphql';
import type { GraphqlContext } from '@/types';
import { createModuleLogger } from '@/utils/logger';
import { CompanyDocument, CompanyModel, LocationDocument, LocationModel, ObiDocument } from '@/models';
import type { FastifyInstance } from 'fastify';
const logger = createModuleLogger('EpikBoxResolver');

@Resolver(EpikBoxDocument)
export class EpikBoxResolver {
  private companyModel: CompanyModel;
  private locationModel: LocationModel;
  private obiService;

  constructor(fastify: FastifyInstance) {
    this.companyModel = new CompanyModel(fastify);
    this.locationModel = new LocationModel(fastify)
    this.obiService = fastify.services.obiService;
  }

  @Authorized([[Features.EDGEDEVICES_LIST, ValidateOperations.ValidateRead]])
  @Query(() => ListEpikBoxPaginatedResult)
  async ListEpikBoxes(
    @Arg('filter', () => EpikBoxFilterInput, { nullable: true }) filter: EpikBoxFilterInput,
    @Arg('pagination', () => PaginationInput, { nullable: true }) pagination: PaginationInput,
    @Ctx() { fastify, request, reply }: GraphqlContext
  ): Promise<InstanceType<typeof ListEpikBoxPaginatedResult>> {
    logger.info('ListEpikBoxes');
    logger.debug({ filter, pagination }, 'ListEpikBoxes');
    const allowed = await fastify.listAllowedCompanies(request);
    filter.isAll = allowed.isAll;
    filter.ids = allowed.ids;
    return fastify.services.epikBoxService.listEpikBoxes(filter, pagination);
  }

  @FieldResolver(() => CompanyDocument, { nullable: true })
  async companyDoc(@Root() box: EpikBoxDocument): Promise<CompanyDocument | null> {
    if (!box.assignedTo) return null;
    return await this.companyModel.findById(box.assignedTo.toString());
  }

  @FieldResolver(() => LocationDocument, { nullable: true })
  async locationDoc(@Root() box: EpikBoxDocument): Promise<LocationDocument | null> {
    if (!box.locRef) return null;
    return await this.locationModel.findById(box.locRef.toString());
  }

  @FieldResolver(() => [ObiDocument], { nullable: true })
  async obiDocs(@Root() box: EpikBoxDocument): Promise<ObiDocument[]> {
    if (!box.Obis || box.Obis.length === 0) return [];
    return this.obiService.listObisByIds(box.Obis);
  }
}
