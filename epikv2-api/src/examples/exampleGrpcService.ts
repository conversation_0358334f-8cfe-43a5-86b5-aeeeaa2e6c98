import { config } from '@/config/environment';
import { createModuleLogger } from '@/utils/logger';
import { GrpcServiceClient, safeGrpcOperation } from '@/utils/grpcClient';
import { FastifyInstance, FastifyRequest } from 'fastify';
import fp from 'fastify-plugin';

// Example: Creating a new gRPC service client
export class ExampleServiceClient extends GrpcServiceClient {
  constructor(serverAddress?: string) {
    super({
      serverAddress: serverAddress || 'localhost:50051', // Replace with your service address
      serviceName: 'ExampleService', // Replace with your service name
      packageName: 'example', // Replace with your package name
      maxRetries: 3,
      retryDelay: 1000
    });
  }

  // Example method - replace with your actual service methods
  async getUserData(userId: string): Promise<any> {
    return this.makeRequest<any>(
      'GetUserData', // gRPC method name
      { user_id: userId }, // request parameters
      'user_data' // optional: specific response field to extract
    );
  }

  // Another example method
  async createUser(userData: any): Promise<any> {
    return this.makeRequest<any>(
      'CreateUser',
      userData
    );
  }

  // Example method with no specific response field extraction
  async deleteUser(userId: string): Promise<any> {
    return this.makeRequest<any>(
      'DeleteUser',
      { user_id: userId }
    );
  }
}

// Create an instance of your service client
const exampleServiceClient = new ExampleServiceClient();
const exampleLogger = createModuleLogger('exampleService');

// Example Fastify plugin for your gRPC service
export const exampleServicePlugin = fp(async function (fastify: FastifyInstance) {
  try {
    await exampleServiceClient.connect();
  } catch (error) {
    fastify.log.warn('Example service not available, will retry on first request');
  }

  // Decorate Fastify with your service methods
  fastify.decorate(
    'getUserData',
    async (request: FastifyRequest, userId: string): Promise<any> => {
      return await safeGrpcOperation(async () => {
        return await exampleServiceClient.getUserData(userId);
      }, 'get user data', exampleLogger);
    }
  );

  fastify.decorate(
    'createUser',
    async (request: FastifyRequest, userData: any): Promise<any> => {
      return await safeGrpcOperation(async () => {
        return await exampleServiceClient.createUser(userData);
      }, 'create user', exampleLogger);
    }
  );

  fastify.decorate(
    'deleteUser',
    async (request: FastifyRequest, userId: string): Promise<boolean> => {
      return await safeGrpcOperation(async () => {
        await exampleServiceClient.deleteUser(userId);
        return true;
      }, 'delete user', exampleLogger, false); // default value is false if operation fails
    }
  );
});

/*
Usage in your routes:

// In your route handler:
app.post('/users', async (request, reply) => {
  const userData = request.body;
  const result = await app.createUser(request, userData);
  return { success: true, data: result };
});

app.get('/users/:id', async (request, reply) => {
  const userId = request.params.id;
  const userData = await app.getUserData(request, userId);
  return userData;
});

app.delete('/users/:id', async (request, reply) => {
  const userId = request.params.id;
  const success = await app.deleteUser(request, userId);
  return { success };
});

// Register the plugin in your main app:
await app.register(exampleServicePlugin);
*/
