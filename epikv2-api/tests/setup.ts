import { beforeAll, afterAll } from 'vitest';
import { MongoMemoryServer } from 'mongodb-memory-server';
import { MongoClient } from 'mongodb';

let mongoServer: MongoMemoryServer;
let mongoClient: MongoClient;

beforeAll(async () => {
  mongoServer = await MongoMemoryServer.create();
  const mongoUri = mongoServer.getUri();
  mongoClient = new MongoClient(mongoUri);
  await mongoClient.connect();

  process.env.MONGODB_URI = mongoUri;
  process.env.JWT_SECRET = 'test-secret';
  process.env.NODE_ENV = 'test';

  console.log('Test environment setup complete');
});

afterAll(async () => {
  if (mongoClient) {
    await mongoClient.close();
  }

  if (mongoServer) {
    await mongoServer.stop();
  }



  console.log('Test environment cleanup complete');
});



// Test utilities
export const _testUtils = {
  getDatabase: () => {
    if (!mongoClient) {
      throw new Error('MongoDB client not initialized');
    }
    return mongoClient.db();
  },



  wait: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),
};

declare global {
  var testUtils: typeof _testUtils;
}

global.testUtils = _testUtils;
