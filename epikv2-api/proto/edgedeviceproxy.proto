syntax = "proto3";

package edge.v1;

message edge {
  message v1 {
    message DeviceRequest {
      string serial_number = 1;
      string path = 2;
      int32 priority = 3;
      bool async = 4;
      .edge.v1.DeviceRequest.HeadersEntry headers = 5;
      .edge.v1.DeviceRequest.MetadataEntry metadata = 6;
    }

    message DeviceResponse {
      int32 status_code = 1;
      bytes body = 2;
      .edge.v1.DeviceResponse.HeadersEntry headers = 3;
      string request_id = 4;
      bool is_async = 5;
    }

    message AsyncRequest {
      string serial_number = 1;
      string path = 2;
      int32 priority = 3;
      .edge.v1.AsyncRequest.HeadersEntry headers = 4;
      .edge.v1.AsyncRequest.MetadataEntry metadata = 5;
    }

    message AsyncResponse {
      string request_id = 1;
      string status = 2;
      int64 estimated_completion_seconds = 3;
    }

    message StatusRequest {
      string request_id = 1;
    }

    message StatusResponse {
      string request_id = 1;
      string status = 2;
      int32 status_code = 3;
      bytes body = 4;
      .edge.v1.StatusResponse.HeadersEntry headers = 5;
      string error = 6;
      int64 created_at = 7;
      int64 completed_at = 8;
    }

    message CancelRequestMessage {
      string request_id = 1;
    }

    message CancelResponse {
      string request_id = 1;
      bool cancelled = 2;
      string message = 3;
    }

    service EdgeDeviceProxy {
      rpc HandleRequest(.edge.v1.DeviceRequest) returns (.edge.v1.DeviceResponse);
      rpc EnqueueRequest(.edge.v1.AsyncRequest) returns (.edge.v1.AsyncResponse);
      rpc GetRequestStatus(.edge.v1.StatusRequest) returns (.edge.v1.StatusResponse);
      rpc CancelRequest(.edge.v1.CancelRequestMessage) returns (.edge.v1.CancelResponse);
    }

  }

}

