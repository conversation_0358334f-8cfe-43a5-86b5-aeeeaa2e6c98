syntax = "proto3";

package edge.v1;

message DeviceRequest {
  message HeadersEntry {
    string key = 1;
    string value = 2;
  }

  message MetadataEntry {
    string key = 1;
    string value = 2;
  }

  string serial_number = 1;
  string path = 2;
  int32 priority = 3;
  bool async = 4;
  repeated HeadersEntry headers = 5;
  repeated MetadataEntry metadata = 6;
}

message DeviceResponse {
  message HeadersEntry {
    string key = 1;
    string value = 2;
  }

  int32 status_code = 1;
  bytes body = 2;
  repeated HeadersEntry headers = 3;
  string request_id = 4;
  bool is_async = 5;
}

message AsyncRequest {
  message HeadersEntry {
    string key = 1;
    string value = 2;
  }

  message MetadataEntry {
    string key = 1;
    string value = 2;
  }

  string serial_number = 1;
  string path = 2;
  int32 priority = 3;
  repeated HeadersEntry headers = 4;
  repeated MetadataEntry metadata = 5;
}

message AsyncResponse {
  string request_id = 1;
  string status = 2;
  int64 estimated_completion_seconds = 3;
}

message StatusRequest {
  string request_id = 1;
}

message StatusResponse {
  message HeadersEntry {
    string key = 1;
    string value = 2;
  }

  string request_id = 1;
  string status = 2;
  int32 status_code = 3;
  bytes body = 4;
  repeated HeadersEntry headers = 5;
  string error = 6;
  int64 created_at = 7;
  int64 completed_at = 8;
}

message CancelRequestMessage {
  string request_id = 1;
}

message CancelResponse {
  string request_id = 1;
  bool cancelled = 2;
  string message = 3;
}

message DashboardEndpoint {
  string path = 1;
  string identifier = 2;
  int32 timeout_seconds = 3;
}

message DashboardRequest {
  string serial_number = 1;
}

message DashboardEndpointResult {
  message HeadersEntry {
    string key = 1;
    string value = 2;
  }

  string identifier = 1;
  string path = 2;
  int32 status_code = 3;
  bytes body = 4;
  repeated HeadersEntry headers = 5;
  string error = 6;
  int64 duration_ms = 7;
  bool success = 8;
}

message DashboardResponse {
  string serial_number = 1;
  repeated DashboardEndpointResult results = 2;
  int32 total_endpoints = 3;
  int32 successful_endpoints = 4;
  int32 failed_endpoints = 5;
  int64 total_duration_ms = 6;
  bool overall_success = 7;
}

service EdgeDeviceProxy {
  rpc HandleRequest(DeviceRequest) returns (DeviceResponse);
  rpc EnqueueRequest(AsyncRequest) returns (AsyncResponse);
  rpc GetRequestStatus(StatusRequest) returns (StatusResponse);
  rpc CancelRequest(CancelRequestMessage) returns (CancelResponse);
  rpc DashboardData(DashboardRequest) returns (DashboardResponse);
}

