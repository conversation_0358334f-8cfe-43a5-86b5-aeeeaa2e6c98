syntax = "proto3";

package edge.v1;

message DeviceRequest {
  message HeadersEntry {
    string key = 1;
    string value = 2;
  }

  message MetadataEntry {
    string key = 1;
    string value = 2;
  }

  string serial_number = 1;
  string path = 2;
  int32 priority = 3;
  bool async = 4;
  repeated HeadersEntry headers = 5;
  repeated MetadataEntry metadata = 6;
}

message DeviceResponse {
  message HeadersEntry {
    string key = 1;
    string value = 2;
  }

  int32 status_code = 1;
  bytes body = 2;
  repeated HeadersEntry headers = 3;
  string request_id = 4;
  bool is_async = 5;
}

message AsyncRequest {
  message HeadersEntry {
    string key = 1;
    string value = 2;
  }

  message MetadataEntry {
    string key = 1;
    string value = 2;
  }

  string serial_number = 1;
  string path = 2;
  int32 priority = 3;
  repeated HeadersEntry headers = 4;
  repeated MetadataEntry metadata = 5;
}

message AsyncResponse {
  string request_id = 1;
  string status = 2;
  int64 estimated_completion_seconds = 3;
}

message StatusRequest {
  string request_id = 1;
}

message StatusResponse {
  message HeadersEntry {
    string key = 1;
    string value = 2;
  }

  string request_id = 1;
  string status = 2;
  int32 status_code = 3;
  bytes body = 4;
  repeated HeadersEntry headers = 5;
  string error = 6;
  int64 created_at = 7;
  int64 completed_at = 8;
}

message CancelRequestMessage {
  string request_id = 1;
}

message CancelResponse {
  string request_id = 1;
  bool cancelled = 2;
  string message = 3;
}

service EdgeDeviceProxy {
  rpc HandleRequest(.edge.v1.DeviceRequest) returns (.edge.v1.DeviceResponse);
  rpc EnqueueRequest(.edge.v1.AsyncRequest) returns (.edge.v1.AsyncResponse);
  rpc GetRequestStatus(.edge.v1.StatusRequest) returns (.edge.v1.StatusResponse);
  rpc CancelRequest(.edge.v1.CancelRequestMessage) returns (.edge.v1.CancelResponse);
}

