syntax = "proto3";

package auth;

message auth {
  message ValidateUserPermissionRequest {
    string user_id = 1;
    string feature_key = 2;
    int32 operation = 3;
  }

  message ValidatePermissionResponse {
    bool has_permission = 1;
    string error_message = 2;
  }

  message ValidateCompanyAccessRequest {
    string user_id = 1;
    string company_id = 2;
  }

  message ValidateEnterpriseAccessRequest {
    string user_id = 1;
    string enterprise_id = 2;
  }

  message ValidateAccessResponse {
    bool has_access = 1;
    string error_message = 2;
  }

  message UserIdInput {
    string user_id = 1;
  }

  message ListCompanyOrEnterpriseResponse {
    repeated string ids = 1;
    bool is_all = 2;
  }

}

