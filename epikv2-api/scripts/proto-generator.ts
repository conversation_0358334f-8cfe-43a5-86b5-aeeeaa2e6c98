#!/usr/bin/env tsx

import { createModuleLogger } from '../src/utils/logger';
import * as fs from 'fs';
import * as path from 'path';
import { Client } from 'grpc-reflection-js';
import * as grpc from '@grpc/grpc-js';
import { exec, spawn } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);
const logger = createModuleLogger('proto-generator');

interface ServiceConfig {
  name: string;
  serverAddress: string;
  serviceName: string;
  packageName: string;
  // Kubernetes-specific options
  useKubectl?: boolean;
  kubeNamespace?: string;
  kubeServiceName?: string;
  kubeServicePort?: number;
  localPort?: number;
  kubeContext?: string;
}

interface ProtoGenerationResult {
  protoFilePath: string;
  typesFilePath?: string;
  success: boolean;
  error?: string;
  portForwardPid?: number;
}

// Define your gRPC services here
const services: ServiceConfig[] = [
  {
    name: 'EdgeDeviceProxy',
    serverAddress: 'localhost:50051', // Will be overridden if using kubectl
    serviceName: 'EdgeDeviceProxy',
    packageName: 'edge.v1',
    useKubectl: true,
    kubeNamespace: 'default',
    kubeServiceName: 'edge-proxy-svc',
    kubeServicePort: 50051,
    localPort: 50051
  }
  // Add more services as needed
];

class ProtoGenerator {
  private protoDir: string;
  private outputDir: string;
  private portForwardProcess: any = null;

  constructor() {
    this.protoDir = path.join(process.cwd(), 'proto');
    this.outputDir = path.join(process.cwd(), 'src', 'types', 'generated');
    this.ensureDirectories();
  }

  private ensureDirectories(): void {
    if (!fs.existsSync(this.protoDir)) {
      fs.mkdirSync(this.protoDir, { recursive: true });
      logger.info(`Created proto directory: ${this.protoDir}`);
    }
    
    if (!fs.existsSync(this.outputDir)) {
      fs.mkdirSync(this.outputDir, { recursive: true });
      logger.info(`Created output directory: ${this.outputDir}`);
    }
  }

  private async setupKubectlPortForward(service: ServiceConfig): Promise<{ serverAddress: string; cleanup: () => Promise<void> }> {
    if (!service.useKubectl) {
      return { 
        serverAddress: service.serverAddress, 
        cleanup: async () => {} 
      };
    }

    const namespace = service.kubeNamespace || 'default';
    const serviceName = service.kubeServiceName || service.serviceName.toLowerCase();
    const servicePort = service.kubeServicePort || 50051;
    const localPort = service.localPort || 50051;

    logger.info(`Setting up kubectl port-forward for service ${serviceName} in namespace ${namespace}`);

    return new Promise((resolve, reject) => {
      this.portForwardProcess = spawn('kubectl', [
        'port-forward',
        ...(service.kubeContext ? [`--context=${service.kubeContext}`] : []),
        `-n`, namespace,
        `service/${serviceName}`,
        `${localPort}:${servicePort}`
      ], {
        stdio: ['ignore', 'pipe', 'pipe']
      });

      let isReady = false;
      const timeout = setTimeout(() => {
        if (!isReady) {
          this.portForwardProcess?.kill();
          reject(new Error('Timeout waiting for kubectl port-forward to be ready'));
        }
      }, 10000); // 10 second timeout

      this.portForwardProcess.stdout?.on('data', (data: Buffer) => {
        const output = data.toString();
        logger.debug(`kubectl stdout: ${output}`);
        
        if (output.includes('Forwarding from') && !isReady) {
          isReady = true;
          clearTimeout(timeout);
          logger.info(`Port forwarding established: localhost:${localPort} -> ${serviceName}:${servicePort}`);
          
          resolve({
            serverAddress: `localhost:${localPort}`,
            cleanup: async () => {
              if (this.portForwardProcess) {
                logger.info('Cleaning up kubectl port-forward process');
                this.portForwardProcess.kill();
                this.portForwardProcess = null;
              }
            }
          });
        }
      });

      this.portForwardProcess.stderr?.on('data', (data: Buffer) => {
        const error = data.toString();
        logger.warn(`kubectl stderr: ${error}`);
        
        if (error.includes('error') || error.includes('Error')) {
          clearTimeout(timeout);
          reject(new Error(`kubectl port-forward failed: ${error}`));
        }
      });

      this.portForwardProcess.on('exit', (code: number) => {
        clearTimeout(timeout);
        if (!isReady) {
          reject(new Error(`kubectl port-forward exited with code ${code}`));
        }
      });
    });
  }

  private jsonToProto(protoJson: any, packageName: string): string {
    let protoContent = '';
    
    // Add syntax declaration
    protoContent += 'syntax = "proto3";\n\n';
    
    // Add package declaration
    if (packageName) {
      protoContent += `package ${packageName};\n\n`;
    }

    // Add imports if any
    if (protoJson.imports && protoJson.imports.length > 0) {
      protoJson.imports.forEach((imp: string) => {
        protoContent += `import "${imp}";\n`;
      });
      protoContent += '\n';
    }

    // Add nested types and services
    if (protoJson.nested) {
      protoContent += this.processNested(protoJson.nested, 0);
    }

    return protoContent;
  }

  private processNested(nested: any, indent: number): string {
    let content = '';
    const indentStr = '  '.repeat(indent);

    for (const [name, definition] of Object.entries(nested)) {
      const def = definition as any;
      
      if (def.methods) {
        // This is a service
        content += `${indentStr}service ${name} {\n`;
        for (const [methodName, method] of Object.entries(def.methods)) {
          const methodDef = method as any;
          content += `${indentStr}  rpc ${methodName}(${methodDef.requestType}) returns (${methodDef.responseType});\n`;
        }
        content += `${indentStr}}\n\n`;
      } else if (def.fields) {
        // This is a message
        content += `${indentStr}message ${name} {\n`;
        for (const [fieldName, field] of Object.entries(def.fields)) {
          const fieldDef = field as any;
          const fieldType = fieldDef.type || 'string';
          const fieldNumber = fieldDef.id || 1;
          content += `${indentStr}  ${fieldType} ${fieldName} = ${fieldNumber};\n`;
        }
        content += `${indentStr}}\n\n`;
      } else if (def.nested) {
        // This is a nested namespace
        content += `${indentStr}message ${name} {\n`;
        content += this.processNested(def.nested, indent + 1);
        content += `${indentStr}}\n\n`;
      }
    }

    return content;
  }

  async generateProtoForService(service: ServiceConfig, forceRegenerate = false): Promise<ProtoGenerationResult> {
    const protoFileName = `${service.serviceName.toLowerCase()}.proto`;
    const protoFilePath = path.join(this.protoDir, protoFileName);
    const typesFilePath = path.join(this.outputDir, `${service.serviceName.toLowerCase()}_pb.ts`);

    // Check if we need to regenerate
    if (!forceRegenerate && fs.existsSync(protoFilePath)) {
      logger.info(`Proto file already exists: ${protoFilePath}`);
      return {
        protoFilePath,
        typesFilePath: fs.existsSync(typesFilePath) ? typesFilePath : undefined,
        success: true
      };
    }

    logger.info(`Downloading proto file for service ${service.serviceName}`);

    // Setup kubectl port forwarding if needed
    const { serverAddress, cleanup } = await this.setupKubectlPortForward(service);
    
    try {
      logger.info(`Connecting to gRPC server at ${serverAddress}`);
      const reflectionClient = new Client(serverAddress, grpc.credentials.createInsecure());

      // List available services
      const services = await reflectionClient.listServices();
      logger.debug('Available services:', services);

      // Find the target service
      const targetServiceName = services.find((svc: string | void) =>
        svc?.toLowerCase().includes(service.serviceName.toLowerCase())
      );

      if (!targetServiceName) {
        throw new Error(`Service ${service.serviceName} not found on server. Available services: ${services.join(', ')}`);
      }

      logger.info(`Found target service: ${targetServiceName}`);

      // Get the proto file descriptor
      const root = await reflectionClient.fileContainingSymbol(targetServiceName);
      const protoJson = root.toJSON();

      // Convert JSON back to proto format
      const protoContent = this.jsonToProto(protoJson, service.packageName);

      // Write proto file
      fs.writeFileSync(protoFilePath, protoContent);
      logger.info(`Proto file saved to: ${protoFilePath}`);

      // Generate TypeScript types if protoc is available
      let generatedTypesPath: string | undefined;
      try {
        generatedTypesPath = await this.generateTypeScriptTypes(service.serviceName);
      } catch (error) {
        logger.warn(`Failed to generate TypeScript types: ${error}`);
      }

      return {
        protoFilePath,
        typesFilePath: generatedTypesPath,
        success: true,
        portForwardPid: this.portForwardProcess?.pid
      };
    } catch (error) {
      logger.error(`Failed to download proto file: ${error}`);
      return {
        protoFilePath,
        typesFilePath,
        success: false,
        error: error instanceof Error ? error.message : String(error),
        portForwardPid: this.portForwardProcess?.pid
      };
    } finally {
      // Clean up port forwarding
      await cleanup();
    }
  }

  private async generateTypeScriptTypes(serviceName: string): Promise<string> {
    const protoFileName = `${serviceName.toLowerCase()}.proto`;
    const protoFilePath = path.join(this.protoDir, protoFileName);
    const typesFilePath = path.join(this.outputDir, `${serviceName.toLowerCase()}_pb.ts`);

    if (!fs.existsSync(protoFilePath)) {
      throw new Error(`Proto file not found: ${protoFilePath}`);
    }

    logger.info(`Generating TypeScript types from ${protoFilePath}`);

    try {
      // Find protoc-gen-ts plugin path
      const pluginPath = await this.findProtocGenTsPlugin();

      // Run protoc command
      const command = `protoc --plugin=protoc-gen-ts=${pluginPath} --ts_out=${this.outputDir} --proto_path=${this.protoDir} ${protoFileName}`;

      logger.debug(`Running command: ${command}`);

      const { stdout, stderr } = await execAsync(command);

      if (stderr) {
        logger.warn(`protoc stderr: ${stderr}`);
      }

      if (stdout) {
        logger.debug(`protoc stdout: ${stdout}`);
      }

      if (fs.existsSync(typesFilePath)) {
        logger.info(`TypeScript types generated successfully: ${typesFilePath}`);
        return typesFilePath;
      } else {
        throw new Error('TypeScript types file was not generated');
      }
    } catch (error) {
      logger.error(`Failed to generate TypeScript types: ${error}`);
      throw error;
    }
  }

  private async findProtocGenTsPlugin(): Promise<string> {
    // Try to find protoc-gen-ts in node_modules
    const possiblePaths = [
      path.join(process.cwd(), 'node_modules', '.bin', 'protoc-gen-ts'),
      path.join(process.cwd(), 'node_modules', 'protoc-gen-ts', 'bin', 'protoc-gen-ts'),
      'protoc-gen-ts' // Assume it's in PATH
    ];

    for (const pluginPath of possiblePaths) {
      try {
        await execAsync(`which ${pluginPath}`);
        return pluginPath;
      } catch {
        // Continue to next path
      }
    }

    throw new Error('protoc-gen-ts plugin not found. Please install it with: npm install --save-dev protoc-gen-ts');
  }
}

// Main execution functions
async function generateProtoForService(service: ServiceConfig, forceRegenerate = false) {
  console.log(`\n🔄 Generating proto files for ${service.name}...`);

  const generator = new ProtoGenerator();

  try {
    const result = await generator.generateProtoForService(service, forceRegenerate);

    if (result.success) {
      console.log(`✅ Successfully generated proto files for ${service.name}`);
      console.log(`   Proto file: ${result.protoFilePath}`);
      if (result.typesFilePath) {
        console.log(`   Types file: ${result.typesFilePath}`);
      }
    } else {
      console.error(`❌ Failed to generate proto files for ${service.name}: ${result.error}`);
    }

    return result.success;
  } catch (error) {
    console.error(`❌ Error generating proto files for ${service.name}:`, error);
    return false;
  }
}

async function generateAllServices(forceRegenerate = false) {
  console.log('🚀 Starting proto generation for all services...\n');

  let successCount = 0;
  let totalCount = services.length;

  for (const service of services) {
    const success = await generateProtoForService(service, forceRegenerate);
    if (success) {
      successCount++;
    }
  }

  console.log(`\n📊 Generation complete: ${successCount}/${totalCount} services successful`);

  if (successCount === totalCount) {
    console.log('🎉 All proto files generated successfully!');
    process.exit(0);
  } else {
    console.log('⚠️  Some proto files failed to generate. Check the logs above.');
    process.exit(1);
  }
}

// CLI handling
async function main() {
  const args = process.argv.slice(2);
  const forceRegenerate = args.includes('--force') || args.includes('-f');
  const serviceName = args.find(arg => !arg.startsWith('--') && !arg.startsWith('-'));

  if (serviceName) {
    // Generate for specific service
    const service = services.find(s => s.name.toLowerCase() === serviceName.toLowerCase());
    if (!service) {
      console.error(`❌ Service '${serviceName}' not found. Available services: ${services.map(s => s.name).join(', ')}`);
      process.exit(1);
    }

    const success = await generateProtoForService(service, forceRegenerate);
    process.exit(success ? 0 : 1);
  } else {
    // Generate for all services
    await generateAllServices(forceRegenerate);
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Fatal error:', error);
    process.exit(1);
  });
}
