#!/usr/bin/env tsx

import { generateProtoAndTypes } from '../src/utils/protoGenerator';
import { config } from '../src/config/environment';

interface ServiceConfig {
  name: string;
  serverAddress: string;
  serviceName: string;
  packageName: string;
}

// Define your gRPC services here
const services: ServiceConfig[] = [
  {
    name: 'EdgeDeviceProxy',
    serverAddress: config.edgeProxyUri,
    serviceName: 'EdgeDeviceProxy',
    packageName: 'edge.v1'
  }
  // Add more services as needed
];

async function generateProtoForService(service: ServiceConfig, forceRegenerate = false) {
  console.log(`\n🔄 Generating proto files for ${service.name}...`);
  
  try {
    const result = await generateProtoAndTypes({
      serverAddress: service.serverAddress,
      serviceName: service.serviceName,
      packageName: service.packageName,
      forceRegenerate
    });

    if (result.success) {
      console.log(`✅ Successfully generated proto files for ${service.name}`);
      console.log(`   Proto file: ${result.protoFilePath}`);
      if (result.typesFilePath) {
        console.log(`   Types file: ${result.typesFilePath}`);
      }
    } else {
      console.error(`❌ Failed to generate proto files for ${service.name}: ${result.error}`);
    }

    return result.success;
  } catch (error) {
    console.error(`❌ Error generating proto files for ${service.name}:`, error);
    return false;
  }
}

async function main() {
  const args = process.argv.slice(2);
  const forceRegenerate = args.includes('--force') || args.includes('-f');
  const serviceName = args.find(arg => !arg.startsWith('-'));

  console.log('🚀 gRPC Proto File Generator');
  console.log('============================');

  if (serviceName) {
    // Generate for specific service
    const service = services.find(s => s.name.toLowerCase() === serviceName.toLowerCase());
    if (!service) {
      console.error(`❌ Service '${serviceName}' not found. Available services:`);
      services.forEach(s => console.log(`   - ${s.name}`));
      process.exit(1);
    }

    const success = await generateProtoForService(service, forceRegenerate);
    process.exit(success ? 0 : 1);
  } else {
    // Generate for all services
    console.log(`Generating proto files for ${services.length} services...`);
    if (forceRegenerate) {
      console.log('🔄 Force regeneration enabled');
    }

    let successCount = 0;
    for (const service of services) {
      const success = await generateProtoForService(service, forceRegenerate);
      if (success) successCount++;
    }

    console.log(`\n📊 Summary: ${successCount}/${services.length} services processed successfully`);
    
    if (successCount === services.length) {
      console.log('🎉 All proto files generated successfully!');
      process.exit(0);
    } else {
      console.log('⚠️  Some proto files failed to generate');
      process.exit(1);
    }
  }
}

// Handle CLI usage
if (require.main === module) {
  main().catch(error => {
    console.error('💥 Unexpected error:', error);
    process.exit(1);
  });
}

export { main as generateProtoFiles };
